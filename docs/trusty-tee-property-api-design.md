# Trusty TEE GP标准属性API统一设计文档 - 基于OP-TEE架构

## 1. 概述

### 1.1 设计目标

本文档为Trusty TEE设计**统一完整的GP标准属性API实现方案**，参考OP-TEE的成熟属性组织方式和枚举机制，实现：

- **统一的12个GP标准属性API**：无"基础"和"扩展"之分，全部按GP标准设计
- **OP-TEE兼容的架构设计**：属性组织、枚举器结构完全参考OP-TEE
- **保留TIPC服务机制**：继续使用TIPC通用TA服务获取内核属性
- **完整的7种属性类型支持**：BOOL、U32、U64、STRING、UUID、IDENTITY、BINARY_BLOCK
- **统一的通信协议**：避免重复定义，确保设计一致性

### 1.2 OP-TEE属性组织参考

**OP-TEE属性系统特点：**
- 三类属性集：TEE_IMPLEMENTATION、CURRENT_TA、CURRENT_CLIENT
- 静态属性数组：tee_props[]、ta_props[]
- 属性枚举器：基于索引的遍历机制
- 属性类型：BOOL、U32、U64、STRING、UUID、IDENTITY、BINARY_BLOCK

**Trusty TEE适配策略：**
- 保持OP-TEE的属性分类和组织方式
- 通过TIPC服务访问内核中的属性数据
- 实现与OP-TEE兼容的枚举器机制
- 保留用户空间的属性缓存机制

### 1.3 GP标准属性API完整列表（12个）

**属性获取API（7个）：**
1. `TEE_GetPropertyAsString` - 获取字符串属性
2. `TEE_GetPropertyAsBool` - 获取布尔属性
3. `TEE_GetPropertyAsU32` - 获取32位整数属性
4. `TEE_GetPropertyAsU64` - 获取64位整数属性
5. `TEE_GetPropertyAsBinaryBlock` - 获取二进制块属性
6. `TEE_GetPropertyAsUUID` - 获取UUID属性
7. `TEE_GetPropertyAsIdentity` - 获取身份属性

**属性枚举API（5个）：**
8. `TEE_AllocatePropertyEnumerator` - 分配属性枚举器
9. `TEE_FreePropertyEnumerator` - 释放属性枚举器
10. `TEE_StartPropertyEnumerator` - 启动属性枚举
11. `TEE_ResetPropertyEnumerator` - 重置属性枚举器
12. `TEE_GetPropertyName` - 获取属性名称
13. `TEE_GetNextProperty` - 获取下一个属性

**实现状态**：
- ✅ **已实现**：4个基础API (String, Bool, U32, U64)
- 📋 **待实现**：3个属性获取API (BinaryBlock, UUID, Identity) + 5个枚举器API

## 2. 基于OP-TEE属性组织的架构设计

### 2.1 OP-TEE vs Trusty TEE属性组织对比

**OP-TEE属性组织：**
```
用户空间 (libutee):
  - 本地静态属性数组 (tee_props[], ta_props[])
  - 属性枚举器 (prop_enumerator)
  ↓ 系统调用 (缺失时)
内核空间 (core/tee):
  - 动态属性查找
  - 枚举器状态管理
```

**Trusty TEE适配组织：**
```
用户空间 (libutee):
  - 本地静态属性数组 (tee_props[], ta_props[])
  - 属性枚举器 (prop_enumerator)
  ↓ TIPC通用TA服务 (缺失时)
内核空间 (kernel/rctee):
  - 通用TA服务处理属性请求
  - 内核属性数据库
```

### 2.2 OP-TEE属性组织结构参考

#### 2.2.1 OP-TEE属性分类

**TEE实现属性 (tee_props[])：**
- `gpd.tee.arith.maxBigIntSize` - 大整数最大位数
- `gpd.tee.sockets.version` - Socket版本
- `gpd.tee.sockets.tcp.version` - TCP Socket版本
- `gpd.tee.internalCore.version` - 内核API版本

**TA属性 (ta_props[])：**
- TA特定的属性（由TA manifest定义）
- 运行时动态属性

**客户端属性：**
- 客户端身份相关属性
- 通常为空或动态获取

#### 2.2.2 属性类型枚举（与OP-TEE保持一致）

```c
enum user_ta_prop_type {
    USER_TA_PROP_TYPE_BOOL,              /* bool */
    USER_TA_PROP_TYPE_U32,               /* uint32_t */
    USER_TA_PROP_TYPE_STRING,            /* zero terminated string of char */
    USER_TA_PROP_TYPE_U64,               /* uint64_t */
    USER_TA_PROP_TYPE_UUID,              /* TEE_UUID */
    USER_TA_PROP_TYPE_IDENTITY,          /* TEE_Identity */
    USER_TA_PROP_TYPE_BINARY_BLOCK,      /* binary block */
    USER_TA_PROP_TYPE_INVALID,           /* invalid value */
};
```

#### 2.2.3 属性结构定义（与OP-TEE保持一致）

```c
struct user_ta_property {
    const char* name;                    /* 属性名称 */
    enum user_ta_prop_type type;         /* 属性类型 */
    const void* value;                   /* 属性值指针 */
};
```

#### 2.2.4 属性枚举器结构（参考OP-TEE）

```c
#define PROP_ENUMERATOR_NOT_STARTED     ((size_t)-1)

struct prop_enumerator {
    size_t idx;                          /* 当前索引 */
    TEE_PropSetHandle prop_set;          /* 属性集句柄 */
};
```

#### 2.2.5 Trusty TEE属性数组定义

```c
/* TEE实现属性 - 参考OP-TEE的tee_props[] */
const struct user_ta_property tee_props[] = {
    {
        "gpd.tee.arith.maxBigIntSize",
        USER_TA_PROP_TYPE_U32,
        &(const uint32_t){CFG_TA_BIGNUM_MAX_BITS}
    },
    {
        "gpd.tee.systemTime.protectionLevel",
        USER_TA_PROP_TYPE_U32,
        &(const uint32_t){100}  /* REE级别保护 */
    },
    {
        "gpd.tee.TAPersistentTime.protectionLevel",
        USER_TA_PROP_TYPE_U32,
        &(const uint32_t){1000} /* TEE硬件级别保护 */
    },
    {
        "gpd.tee.internalCore.version",
        USER_TA_PROP_TYPE_U32,
        &(const uint32_t){TEE_CORE_API_VERSION}
    }
};

/* TA属性 - 由TA定义，参考OP-TEE的ta_props[] */
extern const struct user_ta_property ta_props[];
extern const size_t ta_num_props;
```

### 2.3 TIPC通用TA服务协议设计

#### 2.3.1 属性查找策略（参考OP-TEE）

```c
/* 参考OP-TEE的propset_get函数 */
static TEE_Result propset_get(TEE_PropSetHandle h,
                              const struct user_ta_property** eps,
                              size_t* eps_len) {
    if (h == TEE_PROPSET_CURRENT_TA) {
        *eps = ta_props;
        *eps_len = ta_num_props;
    } else if (h == TEE_PROPSET_CURRENT_CLIENT) {
        *eps = NULL;  /* 客户端属性通过TIPC获取 */
        *eps_len = 0;
    } else if (h == TEE_PROPSET_TEE_IMPLEMENTATION) {
        *eps = tee_props;
        *eps_len = ARRAY_SIZE(tee_props);
    } else {
        return TEE_ERROR_ITEM_NOT_FOUND;
    }
    return TEE_SUCCESS;
}
```

#### 2.3.2 TIPC属性请求协议

```c
/* 属性查找请求 */
struct property_request {
    struct generic_ta_msg_hdr hdr;       /* 消息头 */
    uint32_t prop_set;                   /* 属性集句柄 */
    union {
        struct {
            char name[64];               /* 按名称查找 */
        } by_name;
        struct {
            uint32_t index;              /* 按索引查找 */
        } by_index;
    };
};

/* 属性响应 */
struct property_response {
    int32_t result;                      /* 操作结果 */
    uint32_t prop_type;                  /* 属性类型 */
    char name[64];                       /* 属性名称 */
    union {
        char str_value[256];             /* 字符串值 */
        bool bool_value;                 /* 布尔值 */
        uint32_t u32_value;              /* 32位整数值 */
        uint64_t u64_value;              /* 64位整数值 */
        uint8_t binary_value[256];       /* 二进制值 */
        TEE_UUID uuid_value;             /* UUID值 */
        TEE_Identity identity_value;     /* 身份值 */
    };
    uint32_t value_len;                  /* 值长度 */
};

/* 枚举请求 */
struct property_enum_request {
    struct generic_ta_msg_hdr hdr;       /* 消息头 */
    uint32_t prop_set;                   /* 属性集句柄 */
    uint32_t start_index;                /* 起始索引 */
    uint32_t count;                      /* 请求数量 */
};

/* 枚举响应 */
struct property_enum_response {
    int32_t result;                      /* 操作结果 */
    uint32_t total_count;                /* 总属性数量 */
    uint32_t returned_count;             /* 返回的属性数量 */
    struct {
        char name[64];                   /* 属性名称 */
        uint32_t type;                   /* 属性类型 */
    } properties[16];                    /* 属性列表 */
};
```

#### 2.3.3 TIPC命令类型（统一GP标准设计）

```c
enum generic_ta_cmd {
    GENERIC_TA_CMD_INVALID = 0,

    /* 统一属性获取命令 - GP标准 */
    GENERIC_TA_CMD_GET_PROPERTY_BY_NAME = 1,    /* 按名称获取属性 */
    GENERIC_TA_CMD_GET_PROPERTY_BY_INDEX = 2,   /* 按索引获取属性 */

    /* 属性枚举命令 - GP标准 */
    GENERIC_TA_CMD_ENUM_PROPERTIES = 3,         /* 枚举属性列表 */
    GENERIC_TA_CMD_GET_PROPERTY_COUNT = 4,      /* 获取属性数量 */

    /* 兼容现有命令 - 保持向后兼容 */
    GENERIC_TA_CMD_GET_PROPERTY_STRING = 10,
    GENERIC_TA_CMD_GET_PROPERTY_BOOL = 11,
    GENERIC_TA_CMD_GET_PROPERTY_U32 = 12,
    GENERIC_TA_CMD_GET_PROPERTY_U64 = 13,
};
```

**设计说明**：
- **统一命令**：`GET_PROPERTY_BY_NAME`和`GET_PROPERTY_BY_INDEX`支持所有7种GP属性类型
- **类型识别**：通过`property_response.prop_type`字段标识具体类型
- **向后兼容**：保留现有的分类型命令，确保现有代码正常工作
- **GP标准**：所有7种属性类型（BOOL、U32、U64、STRING、UUID、IDENTITY、BINARY_BLOCK）都通过统一接口支持

### 2.4 GP标准属性API完整规范

#### 2.4.1 属性获取API（7个）

**TEE_GetPropertyAsString**
- **输入**：属性集句柄、属性名称、值缓冲区、缓冲区长度指针
- **输出**：字符串值、实际长度
- **返回值**：TEE_Result错误码
- **状态**：✅ 已实现
- **说明**：支持类型转换，可将BOOL和U32类型转换为字符串

**TEE_GetPropertyAsBool**
- **输入**：属性集句柄、属性名称、值指针
- **输出**：布尔值
- **返回值**：TEE_Result错误码
- **状态**：✅ 已实现
- **说明**：严格类型检查，只接受BOOL类型属性

**TEE_GetPropertyAsU32**
- **输入**：属性集句柄、属性名称、值指针
- **输出**：32位无符号整数值
- **返回值**：TEE_Result错误码
- **状态**：✅ 已实现
- **说明**：严格类型检查，只接受U32类型属性

**TEE_GetPropertyAsU64**
- **输入**：属性集句柄、属性名称、值指针
- **输出**：64位无符号整数值
- **返回值**：TEE_Result错误码
- **状态**：✅ 已实现
- **说明**：严格类型检查，只接受U64类型属性

**TEE_GetPropertyAsBinaryBlock**
- **输入**：属性集句柄、属性名称、值缓冲区、缓冲区长度指针
- **输出**：二进制数据、实际长度
- **返回值**：TEE_Result错误码
- **状态**：📋 待实现
- **说明**：获取二进制块属性，支持任意长度的二进制数据

**TEE_GetPropertyAsUUID**
- **输入**：属性集句柄、属性名称、UUID指针
- **输出**：UUID值
- **返回值**：TEE_Result错误码
- **状态**：📋 待实现
- **说明**：获取UUID类型属性，严格类型检查

**TEE_GetPropertyAsIdentity**
- **输入**：属性集句柄、属性名称、Identity指针
- **输出**：身份信息
- **返回值**：TEE_Result错误码
- **状态**：📋 待实现
- **说明**：获取身份类型属性，包含登录类型和UUID

#### 2.4.2 属性枚举API（5个）

**TEE_AllocatePropertyEnumerator**
- **输入**：枚举器句柄指针
- **输出**：分配的枚举器句柄
- **返回值**：TEE_Result错误码
- **状态**：📋 待实现（OP-TEE风格）
- **说明**：分配新的属性枚举器，初始化为未启动状态

**TEE_FreePropertyEnumerator**
- **输入**：枚举器句柄
- **输出**：无
- **返回值**：无
- **状态**：📋 待实现（OP-TEE风格）
- **说明**：释放属性枚举器及其占用的资源

**TEE_StartPropertyEnumerator**
- **输入**：枚举器句柄、属性集句柄
- **输出**：无
- **返回值**：无
- **状态**：📋 待实现（OP-TEE风格）
- **说明**：启动属性枚举器，关联到指定的属性集

**TEE_ResetPropertyEnumerator**
- **输入**：枚举器句柄
- **输出**：无
- **返回值**：无
- **状态**：📋 待实现（OP-TEE风格）
- **说明**：重置属性枚举器到初始状态

**TEE_GetPropertyName**
- **输入**：枚举器句柄、名称缓冲区、缓冲区长度指针
- **输出**：属性名称、实际长度
- **返回值**：TEE_Result错误码
- **状态**：📋 待实现（OP-TEE风格）
- **说明**：获取当前枚举位置的属性名称

**TEE_GetNextProperty**
- **输入**：枚举器句柄
- **输出**：无
- **返回值**：TEE_Result错误码
- **状态**：📋 待实现（OP-TEE风格）
- **说明**：移动枚举器到下一个属性

## 3. 系统架构设计（基于OP-TEE属性组织 + TIPC服务）

### 3.1 总体架构图（基于OP-TEE枚举设计）

```mermaid
graph TB
    %% 全局设置为从上到下

    %% 第一层: API层 (最顶层)
    subgraph " GP API层 (TEE_API)"
        direction LR
        A["<b>属性获取 API</b><br/>TEE_GetPropertyAsString<br/>TEE_GetPropertyAs...<br/><i>✅ 已实现 (4个)</i>"]
        B["<b>枚举器 API</b><br/>TEE_AllocatePropertyEnumerator<br/>TEE_FreePropertyEnumerator<br/>...<br/><i>🔄 OP-TEE风格设计 (6个)</i>"]
    end

    %% 第二层: 逻辑层 (中间)
    subgraph " 属性集管理层 (TA Library) - 参考OP-TEE"
        direction TB
        subgraph "核心组件"
            direction LR
            C1("<b>propget_get_property</b><br/>统一入口<br/><i>支持伪句柄+枚举器句柄</i>")
            C4("<b>OP-TEE风格枚举器</b><br/>prop_enumerator结构<br/><i>idx + prop_set</i>")
        end

        subgraph "处理路径 - 混合查找机制"
            direction LR
            C2("<b>快速路径 (本地)</b><br/>propset_get<br/><i>tee_props[], ta_props[]</i><br/><i>参考OP-TEE静态数组</i>")
            C5("<b>慢速路径 (远程)</b><br/>TIPC Client<br/><i>get_property_from_tipc_service</i><br/><i>索引映射: idx - eps_len</i>")
        end
    end

    %% 第三层: 服务层 (最底层)
    subgraph "kernel: TIPC通用TA服务层 (tee_supplicant)"
        direction TB
        E1("<b>Property Services</b><br/>Lookup & Enumeration<br/><i>GENERIC_TA_CMD_GET_PROPERTY_*</i><br/><i>GENERIC_TA_CMD_ENUM_PROPERTIES</i>")
        E3("<b>内核/系统属性源</b><br/>kernel_tee_props[]<br/>Dynamic Client Props<br/>Runtime Properties")
    end

    %% 定义连接关系
    A --> C1
    B --> C4

    C1 -- "1. 本地优先查找" --> C2
    C1 -- "2. 本地未找到→远程查找" --> C5

    C4 -- "枚举本地属性 (idx < eps_len)" --> C2
    C4 -- "枚举远程属性 (idx >= eps_len)" --> C5

    C5 --> E1
    E1 -->  E3

    %% 样式定义
    style A fill:#e1f5fe,stroke:#0277bd,stroke-width:2px
    style B fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    style C1 fill:#f3e5f5,stroke:#9c27b0,stroke-width:3px
    style C4 fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    style C2 fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    style C5 fill:#ede7f6,stroke:#5e35b1,stroke-width:2px
    style E1 fill:#e8f5e8,stroke:#4caf50,stroke-width:2px
    style E3 fill:#dcedc8
```

### 3.2 架构组件说明（基于OP-TEE枚举设计）

#### 🔵 第一层：GP API层 (TEE_API)
- **属性获取API**：TEE_GetPropertyAsString等7个API
  - ✅ **已实现**：4个基础API (String, Bool, U32, U64)
  - 📋 **待实现**：3个扩展API (BinaryBlock, UUID, Identity)
- **枚举器API**：TEE_AllocatePropertyEnumerator等6个API
  - 🔄 **OP-TEE风格设计**：参考OP-TEE的简洁枚举器实现
  - 📋 **待实现**：完整的6个枚举器API

#### 🟣 第二层：属性集管理层 (TA Library) - 参考OP-TEE
- **统一入口**：propget_get_property (紫色粗边框，核心组件)
  - 支持伪句柄模式：`TEE_PROPSET_*` 直接属性查找
  - 支持枚举器模式：`struct prop_enumerator*` 基于索引的属性访问
  - 实现OP-TEE的 `is_propset_pseudo_handle()` 判断逻辑
- **OP-TEE风格枚举器**：prop_enumerator结构 (绿色边框，参考OP-TEE)
  - 简洁结构：只有 `idx` (当前索引) 和 `prop_set` (属性集句柄)
  - 索引映射：本地属性 (idx < eps_len) 和远程属性 (idx >= eps_len)
  - 状态管理：`PROP_ENUMERATOR_NOT_STARTED` 初始状态
- **快速路径**：propset_get + 本地属性数组 (红色边框，本地优先)
  - 参考OP-TEE的静态属性数组：`tee_props[]`, `ta_props[]`
  - 三类属性集：TEE_IMPLEMENTATION、CURRENT_TA、CURRENT_CLIENT
  - 本地缓存机制，提供最佳性能
- **慢速路径**：TIPC Client (紫色边框，远程兜底)
  - `get_property_from_tipc_service()` 按名称查找
  - `get_property_by_index_from_tipc_service()` 按索引查找
  - 索引偏移计算：`idx - eps_len` 映射到远程索引

#### 🟢 第三层：TIPC通用TA服务层 (tee_supplicant)
- **Property Services**：Lookup & Enumeration (绿色边框，核心服务)
  - 扩展命令支持：`GENERIC_TA_CMD_GET_PROPERTY_*` 系列
  - 新增枚举命令：`GENERIC_TA_CMD_ENUM_PROPERTIES`
  - 索引查找支持：`GENERIC_TA_CMD_GET_PROPERTY_BY_INDEX`
- **内核属性源**：kernel_tee_props[]、Dynamic Client Props、Runtime Properties
  - 参考OP-TEE的内核属性组织方式
  - 支持动态客户端属性获取
  - 运行时属性扩展机制

### 3.3 业务流程时序图

#### 3.3.1 属性获取流程

```mermaid
sequenceDiagram
    participant TA as TA应用
    participant API as GP API层
    participant Core as propget_get_property
    participant Local as 快速路径(本地)
    participant Remote as 慢速路径(TIPC)
    participant Service as TIPC服务
    participant Kernel as 内核属性源

    Note over TA, Kernel: 属性获取流程
    TA->>API: TEE_GetPropertyAsString("gpd.tee.version")
    API->>Core: 调用统一入口
    Core->>Local: 1. 优先本地查找
    Local->>Local: 搜索tee_props[]数组

    alt 本地找到属性
        Local->>Core: 返回属性值
        Core->>API: 返回结果
        API->>TA: 成功返回
    else 本地未找到
        Core->>Remote: 2. 切换远程路径
        Remote->>Service: TIPC调用属性服务
        Service->>Kernel: 查找内核属性源
        Kernel->>Service: 返回属性值
        Service->>Remote: TIPC响应
        Remote->>Core: 返回结果
        Core->>API: 返回结果
        API->>TA: 成功返回
    end
```

#### 3.3.2 属性枚举流程

```mermaid
sequenceDiagram
    participant TA as TA应用
    participant EnumAPI as 枚举器API
    participant EnumMgmt as enumerator_mgmt
    participant Local as 本地属性数组
    participant Remote as TIPC远程服务
    participant Service as 属性枚举服务
    participant Kernel as 内核属性源

    Note over TA, Kernel: 属性枚举流程
    TA->>EnumAPI: TEE_AllocatePropertyEnumerator()
    EnumAPI->>EnumMgmt: 分配枚举器
    EnumMgmt->>TA: 返回枚举器句柄

    TA->>EnumAPI: TEE_StartPropertyEnumerator(enum, TEE_PROPSET_TEE_IMPLEMENTATION)
    EnumAPI->>EnumMgmt: 启动枚举器
    EnumMgmt->>Local: 获取本地属性集
    Local->>EnumMgmt: 返回本地属性数量
    EnumMgmt->>Remote: 获取远程属性数量
    Remote->>Service: TIPC枚举请求
    Service->>Kernel: 查询内核属性源
    Kernel->>Service: 返回属性列表
    Service->>Remote: TIPC响应
    Remote->>EnumMgmt: 返回远程属性数量
    EnumMgmt->>TA: 枚举器就绪

    loop 遍历所有属性
        TA->>EnumAPI: TEE_GetPropertyName(enum)
        EnumAPI->>EnumMgmt: 获取当前属性名

        alt 当前索引在本地范围
            EnumMgmt->>Local: 获取本地属性名
            Local->>EnumMgmt: 返回属性名
        else 当前索引在远程范围
            EnumMgmt->>Remote: 获取远程属性名
            Remote->>Service: TIPC查询请求
            Service->>Kernel: 查找属性名
            Kernel->>Service: 返回属性名
            Service->>Remote: TIPC响应
            Remote->>EnumMgmt: 返回属性名
        end

        EnumMgmt->>TA: 返回属性名
        TA->>EnumAPI: TEE_GetNextProperty(enum)
        EnumAPI->>EnumMgmt: 移动到下一个属性
        EnumMgmt->>TA: 成功/结束
    end

    TA->>EnumAPI: TEE_FreePropertyEnumerator(enum)
    EnumAPI->>EnumMgmt: 释放枚举器
    EnumMgmt->>TA: 完成
```

### 3.4 业务流程总结（基于OP-TEE枚举设计）

**属性获取流程**：当TA应用调用GP属性API时，请求通过统一入口`propget_get_property`进行智能路由——采用OP-TEE的`is_propset_pseudo_handle()`判断机制，优先在本地静态属性数组（`tee_props[]`, `ta_props[]`）中快速查找以获得最佳性能，若本地未找到则自动切换到TIPC远程服务进行内核属性查找，整个过程对TA应用完全透明，实现了**本地优先、远程兜底**的高效访问策略。

**属性枚举流程**：采用OP-TEE风格的简洁枚举器设计，`struct prop_enumerator`只包含`idx`和`prop_set`两个核心字段，通过索引映射机制统一管理本地和远程属性源——当`idx < eps_len`时访问本地属性数组，当`idx >= eps_len`时通过`idx - eps_len`偏移访问TIPC远程属性，为TA应用提供了**跨数据源、统一视图**的完整属性枚举能力，确保无论属性存储在本地数组还是内核空间都能被透明访问，完全兼容OP-TEE的枚举器使用模式。



### 3.2 属性获取流程图

```mermaid
graph TB
    Start([开始]) --> CheckHandle{检查属性集句柄}
    CheckHandle -->|伪句柄| LocalSearch[本地属性搜索]
    CheckHandle -->|枚举器句柄| EnumSearch[枚举器属性搜索]

    LocalSearch --> Found{找到属性?}
    Found -->|是| TypeConvert[类型转换]
    Found -->|否| RemoteSearch[远程服务搜索]

    EnumSearch --> EnumFound{枚举器中找到?}
    EnumFound -->|是| TypeConvert
    EnumFound -->|否| Error[返回错误]

    RemoteSearch --> Connect[连接Generic TA服务]
    Connect --> SendReq[发送属性请求]
    SendReq --> WaitResp[等待响应]
    WaitResp --> ParseResp[解析响应]
    ParseResp --> TypeConvert

    TypeConvert --> Success[返回成功]
    Error --> End([结束])
    Success --> End
```

### 3.3 属性枚举流程图

```mermaid
graph TB
    Start([开始枚举]) --> Alloc[分配枚举器]
    Alloc --> StartEnum[启动枚举器]
    StartEnum --> SetPropSet[设置属性集]
    SetPropSet --> InitIndex[初始化索引]

    InitIndex --> GetNext[获取下一个属性]
    GetNext --> HasMore{还有属性?}
    HasMore -->|是| ReturnProp[返回属性信息]
    HasMore -->|否| EndEnum[结束枚举]

    ReturnProp --> GetNext
    EndEnum --> FreeEnum[释放枚举器]
    FreeEnum --> End([结束])
```

## 4. 实现方案（参考OP-TEE属性组织 + TIPC服务）

### 4.1 用户空间API实现（lib/libutee/tee_api_property.c）

#### 4.1.1 核心属性获取函数（参考OP-TEE + TIPC扩展）

```c
/* 参考OP-TEE的propget_get_property函数，但使用TIPC替代系统调用 */
static TEE_Result propget_get_property(TEE_PropSetHandle h, const char* name,
                                       enum user_ta_prop_type* type,
                                       void* buf, uint32_t* len) {
    TEE_Result res;
    const struct user_ta_property* eps;
    size_t eps_len;
    size_t n;

/* OP-TEE风格的句柄判断函数 */
static bool is_propset_pseudo_handle(TEE_PropSetHandle h) {
    return (h == TEE_PROPSET_TEE_IMPLEMENTATION ||
            h == TEE_PROPSET_CURRENT_TA ||
            h == TEE_PROPSET_CURRENT_CLIENT);
}

/* 扩展属性获取函数 - 支持所有7种GP标准类型 */
static TEE_Result propget_get_ext_prop(const struct user_ta_property* prop,
                                       enum user_ta_prop_type* type,
                                       void* buf, uint32_t* len) {
    if (*type != USER_TA_PROP_TYPE_INVALID && *type != prop->type)
        return TEE_ERROR_BAD_FORMAT;

    *type = prop->type;

    switch (prop->type) {
    case USER_TA_PROP_TYPE_BOOL:
        if (*len < sizeof(bool)) {
            *len = sizeof(bool);
            return TEE_ERROR_SHORT_BUFFER;
        }
        *(bool*)buf = *(bool*)prop->value;
        *len = sizeof(bool);
        break;
    case USER_TA_PROP_TYPE_U32:
        if (*len < sizeof(uint32_t)) {
            *len = sizeof(uint32_t);
            return TEE_ERROR_SHORT_BUFFER;
        }
        *(uint32_t*)buf = *(uint32_t*)prop->value;
        *len = sizeof(uint32_t);
        break;
    case USER_TA_PROP_TYPE_U64:
        if (*len < sizeof(uint64_t)) {
            *len = sizeof(uint64_t);
            return TEE_ERROR_SHORT_BUFFER;
        }
        *(uint64_t*)buf = *(uint64_t*)prop->value;
        *len = sizeof(uint64_t);
        break;
    case USER_TA_PROP_TYPE_STRING:
        size_t str_len = strlen((char*)prop->value) + 1;
        if (*len < str_len) {
            *len = str_len;
            return TEE_ERROR_SHORT_BUFFER;
        }
        strcpy((char*)buf, (char*)prop->value);
        *len = str_len;
        break;
    case USER_TA_PROP_TYPE_UUID:
        if (*len < sizeof(TEE_UUID)) {
            *len = sizeof(TEE_UUID);
            return TEE_ERROR_SHORT_BUFFER;
        }
        memcpy(buf, prop->value, sizeof(TEE_UUID));
        *len = sizeof(TEE_UUID);
        break;
    case USER_TA_PROP_TYPE_IDENTITY:
        if (*len < sizeof(TEE_Identity)) {
            *len = sizeof(TEE_Identity);
            return TEE_ERROR_SHORT_BUFFER;
        }
        memcpy(buf, prop->value, sizeof(TEE_Identity));
        *len = sizeof(TEE_Identity);
        break;
    case USER_TA_PROP_TYPE_BINARY_BLOCK:
        struct binary_property* bp = (struct binary_property*)prop->value;
        if (*len < bp->length) {
            *len = bp->length;
            return TEE_ERROR_SHORT_BUFFER;
        }
        memcpy(buf, bp->data, bp->length);
        *len = bp->length;
        break;
    default:
        return TEE_ERROR_BAD_FORMAT;
    }

    return TEE_SUCCESS;
}

    if (is_propset_pseudo_handle(h)) {
        /* 首先在本地静态属性中查找 - 参考OP-TEE */
        res = propset_get(h, &eps, &eps_len);
        if (res != TEE_SUCCESS)
            return res;

        for (n = 0; n < eps_len; n++) {
            if (!strcmp(name, eps[n].name))
                return propget_get_ext_prop(eps + n, type, buf, len);
        }

        /* 本地未找到，通过TIPC服务查找 */
        return get_property_from_tipc_service(h, name, type, buf, len);
    } else {
        /* 枚举器句柄处理 - 参考OP-TEE */
        struct prop_enumerator* pe = (struct prop_enumerator*)h;
        size_t idx = pe->idx;

        if (idx == PROP_ENUMERATOR_NOT_STARTED)
            return TEE_ERROR_ITEM_NOT_FOUND;

        res = propset_get(pe->prop_set, &eps, &eps_len);
        if (res != TEE_SUCCESS)
            return res;

        if (idx < eps_len)
            return propget_get_ext_prop(eps + idx, type, buf, len);

        /* 超出本地范围，通过TIPC服务获取 */
        idx -= eps_len;
        return get_property_by_index_from_tipc_service(pe->prop_set, idx,
                                                       name, type, buf, len);
    }
}

/* TIPC响应数据复制函数 */
static TEE_Result copy_property_value_to_buffer(struct property_response* resp,
                                                enum user_ta_prop_type* type,
                                                void* buf, uint32_t* len) {
    *type = resp->prop_type;

    switch (resp->prop_type) {
    case USER_TA_PROP_TYPE_BOOL:
        if (*len < sizeof(bool)) {
            *len = sizeof(bool);
            return TEE_ERROR_SHORT_BUFFER;
        }
        *(bool*)buf = resp->bool_value;
        *len = sizeof(bool);
        break;
    case USER_TA_PROP_TYPE_U32:
        if (*len < sizeof(uint32_t)) {
            *len = sizeof(uint32_t);
            return TEE_ERROR_SHORT_BUFFER;
        }
        *(uint32_t*)buf = resp->u32_value;
        *len = sizeof(uint32_t);
        break;
    case USER_TA_PROP_TYPE_STRING:
        size_t str_len = strlen(resp->str_value) + 1;
        if (*len < str_len) {
            *len = str_len;
            return TEE_ERROR_SHORT_BUFFER;
        }
        strcpy((char*)buf, resp->str_value);
        *len = str_len;
        break;
    case USER_TA_PROP_TYPE_UUID:
        if (*len < sizeof(TEE_UUID)) {
            *len = sizeof(TEE_UUID);
            return TEE_ERROR_SHORT_BUFFER;
        }
        memcpy(buf, &resp->uuid_value, sizeof(TEE_UUID));
        *len = sizeof(TEE_UUID);
        break;
    case USER_TA_PROP_TYPE_IDENTITY:
        if (*len < sizeof(TEE_Identity)) {
            *len = sizeof(TEE_Identity);
            return TEE_ERROR_SHORT_BUFFER;
        }
        memcpy(buf, &resp->identity_value, sizeof(TEE_Identity));
        *len = sizeof(TEE_Identity);
        break;
    case USER_TA_PROP_TYPE_BINARY_BLOCK:
        if (*len < resp->value_len) {
            *len = resp->value_len;
            return TEE_ERROR_SHORT_BUFFER;
        }
        memcpy(buf, resp->binary_value, resp->value_len);
        *len = resp->value_len;
        break;
    default:
        return TEE_ERROR_BAD_FORMAT;
    }

    return TEE_SUCCESS;
}

/* 系统错误码转换函数 */
static TEE_Result sys_to_tee_error(int sys_error) {
    switch (sys_error) {
    case NO_ERROR:
        return TEE_SUCCESS;
    case ERR_NOT_FOUND:
        return TEE_ERROR_ITEM_NOT_FOUND;
    case ERR_NO_MEMORY:
        return TEE_ERROR_OUT_OF_MEMORY;
    case ERR_INVALID_ARGS:
        return TEE_ERROR_BAD_PARAMETERS;
    case ERR_ACCESS_DENIED:
        return TEE_ERROR_ACCESS_DENIED;
    case ERR_BUSY:
        return TEE_ERROR_BUSY;
    case ERR_NOT_SUPPORTED:
        return TEE_ERROR_NOT_SUPPORTED;
    default:
        return TEE_ERROR_GENERIC;
    }
}

/* TIPC服务属性获取函数 */
static TEE_Result get_property_from_tipc_service(TEE_PropSetHandle prop_set,
                                                  const char* name,
                                                  enum user_ta_prop_type* type,
                                                  void* buf, uint32_t* len) {
    handle_t chan;
    int rc = tipc_connect(&chan, GENERIC_TA_PORT);
    if (rc < 0) {
        TLOGE("Failed to connect to service (%d)\n", rc);
        return TEE_ERROR_COMMUNICATION;
    }

    struct property_request req;
    memset(&req, 0, sizeof(req));
    req.hdr.cmd = GENERIC_TA_CMD_GET_PROPERTY_BY_NAME;
    req.prop_set = (uint32_t)prop_set;
    strncpy(req.by_name.name, name, sizeof(req.by_name.name) - 1);

    /* 发送请求 */
    rc = tipc_send1(chan, &req, sizeof(req));
    if (rc < 0) {
        close(chan);
        return TEE_ERROR_COMMUNICATION;
    }

    /* 接收响应 */
    struct property_response resp;
    rc = property_read(chan, sizeof(resp), &resp, sizeof(resp));
    close(chan);

    if (rc < 0)
        return TEE_ERROR_COMMUNICATION;

    if (resp.result != NO_ERROR)
        return sys_to_tee_error(resp.result);

    /* 复制属性值 */
    return copy_property_value_to_buffer(&resp, type, buf, len);
}
```

#### 4.1.2 属性枚举器实现（参考OP-TEE）

```c
/* OP-TEE风格的枚举器结构和常量定义 */
#define PROP_ENUMERATOR_NOT_STARTED ((size_t)-1)

struct prop_enumerator {
    size_t idx;                          /* 当前索引位置 */
    TEE_PropSetHandle prop_set;          /* 关联的属性集句柄 */
};

/* 二进制块属性结构 */
struct binary_property {
    const uint8_t* data;                 /* 二进制数据指针 */
    size_t length;                       /* 数据长度 */
};

/* TIPC枚举协议结构 */
struct property_enum_request {
    uint32_t prop_set;                   /* 属性集类型 */
    uint32_t start_index;                /* 起始索引 */
    uint32_t count;                      /* 请求数量 */
};

struct property_info {
    char name[64];                       /* 属性名称 */
    uint32_t type;                       /* 属性类型 */
};

struct property_enum_response {
    int32_t result;                      /* 操作结果 */
    uint32_t total_count;                /* 总属性数量 */
    uint32_t returned_count;             /* 返回的属性数量 */
    struct property_info properties[16]; /* 属性信息数组 */
};

/* TEE_AllocatePropertyEnumerator - 参考OP-TEE实现 */
TEE_Result TEE_AllocatePropertyEnumerator(TEE_PropSetHandle* enumerator) {
    struct prop_enumerator* pe;

    if (!enumerator)
        return TEE_ERROR_BAD_PARAMETERS;

    pe = TEE_Malloc(sizeof(struct prop_enumerator),
                    TEE_USER_MEM_HINT_NO_FILL_ZERO);
    if (!pe)
        return TEE_ERROR_OUT_OF_MEMORY;

    *enumerator = (TEE_PropSetHandle)pe;
    TEE_ResetPropertyEnumerator(*enumerator);
    return TEE_SUCCESS;
}

/* TEE_FreePropertyEnumerator - 参考OP-TEE实现 */
void TEE_FreePropertyEnumerator(TEE_PropSetHandle enumerator) {
    struct prop_enumerator* pe = (struct prop_enumerator*)enumerator;
    TEE_Free(pe);
}

/* TEE_StartPropertyEnumerator - 参考OP-TEE实现 */
void TEE_StartPropertyEnumerator(TEE_PropSetHandle enumerator,
                                 TEE_PropSetHandle propSet) {
    struct prop_enumerator* pe = (struct prop_enumerator*)enumerator;

    if (!pe)
        return;

    pe->idx = 0;
    pe->prop_set = propSet;
}

/* TEE_ResetPropertyEnumerator - 参考OP-TEE实现 */
void TEE_ResetPropertyEnumerator(TEE_PropSetHandle enumerator) {
    struct prop_enumerator* pe = (struct prop_enumerator*)enumerator;
    pe->idx = PROP_ENUMERATOR_NOT_STARTED;
}

/* TEE_GetPropertyName - 参考OP-TEE实现 */
TEE_Result TEE_GetPropertyName(TEE_PropSetHandle enumerator,
                               void* nameBuffer, size_t* nameBufferLen) {
    struct prop_enumerator* pe = (struct prop_enumerator*)enumerator;
    const struct user_ta_property* eps;
    size_t eps_len;
    const char* str;
    size_t bufferlen;
    TEE_Result res;

    if (!pe)
        return TEE_ERROR_BAD_PARAMETERS;

    bufferlen = *nameBufferLen;
    res = propset_get(pe->prop_set, &eps, &eps_len);
    if (res != TEE_SUCCESS)
        return res;

    if (pe->idx < eps_len) {
        /* 本地属性 */
        str = eps[pe->idx].name;
        bufferlen = strlcpy(nameBuffer, str, *nameBufferLen) + 1;
        if (bufferlen > *nameBufferLen)
            res = TEE_ERROR_SHORT_BUFFER;
        *nameBufferLen = bufferlen;
    } else {
        /* TIPC属性名称获取函数 */
        static TEE_Result get_property_name_from_tipc_service(TEE_PropSetHandle prop_set,
                                                              size_t index,
                                                              void* nameBuffer,
                                                              size_t* nameBufferLen) {
            struct property_enum_request req = {
                .prop_set = prop_set,
                .start_index = index,
                .count = 1
            };
            struct property_enum_response resp;
            TEE_Result res;

            res = tipc_send_property_enum_request(&req, &resp);
            if (res != TEE_SUCCESS)
                return res;

            if (resp.returned_count == 0)
                return TEE_ERROR_ITEM_NOT_FOUND;

            size_t name_len = strlen(resp.properties[0].name) + 1;
            if (*nameBufferLen < name_len) {
                *nameBufferLen = name_len;
                return TEE_ERROR_SHORT_BUFFER;
            }

            strcpy((char*)nameBuffer, resp.properties[0].name);
            *nameBufferLen = name_len;
            return TEE_SUCCESS;
        }

        /* TIPC服务属性 */
        res = get_property_name_from_tipc_service(pe->prop_set,
                                                  pe->idx - eps_len,
                                                  nameBuffer, nameBufferLen);
    }

    return res;
}

/* TEE_GetNextProperty - 参考OP-TEE实现 */
TEE_Result TEE_GetNextProperty(TEE_PropSetHandle enumerator) {
    struct prop_enumerator* pe = (struct prop_enumerator*)enumerator;
    const struct user_ta_property* eps;
    size_t eps_len;
    size_t next_idx;
    TEE_Result res;

    if (!pe)
        return TEE_ERROR_BAD_PARAMETERS;

    if (pe->idx == PROP_ENUMERATOR_NOT_STARTED)
        return TEE_ERROR_ITEM_NOT_FOUND;

    res = propset_get(pe->prop_set, &eps, &eps_len);
    if (res != TEE_SUCCESS)
        return res;

    next_idx = pe->idx + 1;
    pe->idx = next_idx;

    if (next_idx < eps_len) {
        /* 还在本地属性范围内 */
        return TEE_SUCCESS;
    } else {
        /* 检查TIPC服务是否还有更多属性 */
        return check_tipc_property_exists(pe->prop_set, next_idx - eps_len);
    }
}
```

## 5. OP-TEE风格的属性枚举器设计

### 5.1 枚举器数据结构（参考OP-TEE）

#### 5.1.1 简洁的枚举器结构

```c
#define PROP_ENUMERATOR_NOT_STARTED     ((size_t)-1)

struct prop_enumerator {
    size_t idx;                          /* 当前索引 */
    TEE_PropSetHandle prop_set;          /* 属性集句柄 */
};
```

**设计特点**：
- 简洁的结构，只包含必要字段
- 直接使用 `TEE_PropSetHandle` 作为枚举器句柄类型
- 与OP-TEE的设计保持一致

### 5.2 枚举器API设计（参考OP-TEE）

#### 5.2.1 TEE_AllocatePropertyEnumerator

| 项目 | 内容 |
|------|------|
| 函数接口 | `TEE_Result TEE_AllocatePropertyEnumerator(TEE_PropSetHandle* enumerator)` |
| 输入 | enumerator: 枚举器句柄指针 |
| 输出 | enumerator: 分配的枚举器句柄 |
| 返回值 | TEE_SUCCESS: 成功分配<br>TEE_ERROR_BAD_PARAMETERS: 参数无效<br>TEE_ERROR_OUT_OF_MEMORY: 内存不足 |
| 说明 | 分配一个新的属性枚举器，初始化为未启动状态 |

#### 5.2.2 TEE_FreePropertyEnumerator

| 项目 | 内容 |
|------|------|
| 函数接口 | `void TEE_FreePropertyEnumerator(TEE_PropSetHandle enumerator)` |
| 输入 | enumerator: 枚举器句柄 |
| 输出 | 无 |
| 返回值 | 无 |
| 说明 | 释放属性枚举器及其占用的资源 |

#### 5.2.3 TEE_StartPropertyEnumerator

| 项目 | 内容 |
|------|------|
| 函数接口 | `void TEE_StartPropertyEnumerator(TEE_PropSetHandle enumerator, TEE_PropSetHandle propSet)` |
| 输入 | enumerator: 枚举器句柄<br>propSet: 属性集句柄 |
| 输出 | 无 |
| 返回值 | 无 |
| 说明 | 启动属性枚举器，关联到指定的属性集 |

#### 5.2.4 TEE_ResetPropertyEnumerator

| 项目 | 内容 |
|------|------|
| 函数接口 | `void TEE_ResetPropertyEnumerator(TEE_PropSetHandle enumerator)` |
| 输入 | enumerator: 枚举器句柄 |
| 输出 | 无 |
| 返回值 | 无 |
| 说明 | 重置属性枚举器到初始状态 |

#### 5.2.5 TEE_GetPropertyName

| 项目 | 内容 |
|------|------|
| 函数接口 | `TEE_Result TEE_GetPropertyName(TEE_PropSetHandle enumerator, void* nameBuffer, size_t* nameBufferLen)` |
| 输入 | enumerator: 枚举器句柄<br>nameBuffer: 名称缓冲区<br>nameBufferLen: 缓冲区长度指针 |
| 输出 | nameBuffer: 属性名称<br>nameBufferLen: 实际长度 |
| 返回值 | TEE_SUCCESS: 成功获取<br>TEE_ERROR_BAD_PARAMETERS: 参数无效<br>TEE_ERROR_SHORT_BUFFER: 缓冲区不足<br>TEE_ERROR_ITEM_NOT_FOUND: 没有更多属性 |
| 说明 | 获取当前枚举位置的属性名称 |

#### 5.2.6 TEE_GetNextProperty

| 项目 | 内容 |
|------|------|
| 函数接口 | `TEE_Result TEE_GetNextProperty(TEE_PropSetHandle enumerator)` |
| 输入 | enumerator: 枚举器句柄 |
| 输出 | 无 |
| 返回值 | TEE_SUCCESS: 成功移动到下一个<br>TEE_ERROR_BAD_PARAMETERS: 参数无效<br>TEE_ERROR_ITEM_NOT_FOUND: 没有更多属性 |
| 说明 | 移动枚举器到下一个属性 |

## 6. GP标准数据类型定义

### 6.1 完整属性类型支持

所有GP标准属性类型已在第2.2.2节中完整定义，包括：

```c
enum user_ta_prop_type {
    USER_TA_PROP_TYPE_BOOL,              /* bool */
    USER_TA_PROP_TYPE_U32,               /* uint32_t */
    USER_TA_PROP_TYPE_STRING,            /* zero terminated string of char */
    USER_TA_PROP_TYPE_U64,               /* uint64_t */
    USER_TA_PROP_TYPE_UUID,              /* TEE_UUID */
    USER_TA_PROP_TYPE_IDENTITY,          /* TEE_Identity */
    USER_TA_PROP_TYPE_BINARY_BLOCK,      /* binary block */
    USER_TA_PROP_TYPE_INVALID,           /* invalid value */
};
```

### 6.2 GP标准数据结构

**TEE_UUID结构**（GP标准）：
```c
typedef struct {
    uint32_t timeLow;
    uint16_t timeMid;
    uint16_t timeHiAndVersion;
    uint8_t clockSeqAndNode[8];
} TEE_UUID;
```

**TEE_Identity结构**（GP标准）：
```c
typedef struct {
    uint32_t login;                      /* 登录类型 */
    TEE_UUID uuid;                       /* 身份UUID */
} TEE_Identity;
```

### 6.3 统一通信协议

TIPC协议中的`property_response`结构支持所有GP标准类型：

```c
struct property_response {
    int32_t result;                      /* 操作结果 */
    uint32_t prop_type;                  /* 属性类型 */
    char name[64];                       /* 属性名称 */
    union {
        char str_value[256];             /* 字符串值 */
        bool bool_value;                 /* 布尔值 */
        uint32_t u32_value;              /* 32位整数值 */
        uint64_t u64_value;              /* 64位整数值 */
        uint8_t binary_value[256];       /* 二进制值 */
        TEE_UUID uuid_value;             /* UUID值 */
        TEE_Identity identity_value;     /* 身份值 */
    };
    uint32_t value_len;                  /* 值长度 */
};
```

## 7. 完整实现方案

### 7.1 头文件更新

#### 7.1.1 tee_ta_api.h - GP标准属性API完整声明

```c
#ifndef TEE_TA_API_H
#define TEE_TA_API_H

#include <tee_api_defines.h>
#include <tee_api_types.h>

/* GP标准属性获取API（7个） */
TEE_Result TEE_GetPropertyAsString(TEE_PropSetHandle propsetOrEnumerator,
                                   const char* name, char* valueBuffer,
                                   size_t* valueBufferLen);

TEE_Result TEE_GetPropertyAsBool(TEE_PropSetHandle propsetOrEnumerator,
                                 const char* name, bool* value);

TEE_Result TEE_GetPropertyAsU32(TEE_PropSetHandle propsetOrEnumerator,
                                const char* name, uint32_t* value);

TEE_Result TEE_GetPropertyAsU64(TEE_PropSetHandle propsetOrEnumerator,
                                const char* name, uint64_t* value);

TEE_Result TEE_GetPropertyAsBinaryBlock(TEE_PropSetHandle propsetOrEnumerator,
                                        const char* name,
                                        void* valueBuffer,
                                        size_t* valueBufferLen);

TEE_Result TEE_GetPropertyAsUUID(TEE_PropSetHandle propsetOrEnumerator,
                                 const char* name,
                                 TEE_UUID* value);

TEE_Result TEE_GetPropertyAsIdentity(TEE_PropSetHandle propsetOrEnumerator,
                                     const char* name,
                                     TEE_Identity* value);

/* GP标准属性枚举API（5个） - OP-TEE风格 */
TEE_Result TEE_AllocatePropertyEnumerator(TEE_PropSetHandle* enumerator);

void TEE_FreePropertyEnumerator(TEE_PropSetHandle enumerator);

void TEE_StartPropertyEnumerator(TEE_PropSetHandle enumerator,
                                 TEE_PropSetHandle propSet);

void TEE_ResetPropertyEnumerator(TEE_PropSetHandle enumerator);

TEE_Result TEE_GetPropertyName(TEE_PropSetHandle enumerator,
                               void* nameBuffer,
                               size_t* nameBufferLen);

TEE_Result TEE_GetNextProperty(TEE_PropSetHandle enumerator);

#endif /* TEE_TA_API_H */
```

**注意**：枚举器API使用OP-TEE风格的`TEE_PropSetHandle`类型，而不是单独的`TEE_PropertyEnumeratorHandle`类型，这与OP-TEE的设计保持一致。

### 7.2 GP标准API完整实现

#### 7.2.1 属性获取API实现（基于统一的propget_get_property）

**TEE_GetPropertyAsBinaryBlock实现**：
```c
TEE_Result TEE_GetPropertyAsBinaryBlock(TEE_PropSetHandle propsetOrEnumerator,
                                        const char* name,
                                        void* valueBuffer,
                                        size_t* valueBufferLen) {
    TEE_Result res;
    enum user_ta_prop_type type = USER_TA_PROP_TYPE_BINARY_BLOCK;
    uint32_t tmp_len = *valueBufferLen;

    if (!valueBuffer || !valueBufferLen)
        return TEE_ERROR_BAD_PARAMETERS;

    res = propget_get_property(propsetOrEnumerator, name, &type, valueBuffer, &tmp_len);
    if (res != TEE_SUCCESS) {
        if (res == TEE_ERROR_SHORT_BUFFER) {
            *valueBufferLen = tmp_len;
        }
        return res;
    }

    if (type != USER_TA_PROP_TYPE_BINARY_BLOCK)
        return TEE_ERROR_BAD_FORMAT;

    *valueBufferLen = tmp_len;
    return TEE_SUCCESS;
}
```

**TEE_GetPropertyAsUUID实现**：
```c
TEE_Result TEE_GetPropertyAsUUID(TEE_PropSetHandle propsetOrEnumerator,
                                 const char* name,
                                 TEE_UUID* value) {
    TEE_Result res;
    enum user_ta_prop_type type = USER_TA_PROP_TYPE_UUID;
    uint32_t uuid_len = sizeof(TEE_UUID);

    if (!value)
        return TEE_ERROR_BAD_PARAMETERS;

    res = propget_get_property(propsetOrEnumerator, name, &type, value, &uuid_len);
    if (type != USER_TA_PROP_TYPE_UUID)
        res = TEE_ERROR_BAD_FORMAT;

    return res;
}
```

**TEE_GetPropertyAsIdentity实现**：
```c
TEE_Result TEE_GetPropertyAsIdentity(TEE_PropSetHandle propsetOrEnumerator,
                                     const char* name,
                                     TEE_Identity* value) {
    TEE_Result res;
    enum user_ta_prop_type type = USER_TA_PROP_TYPE_IDENTITY;
    uint32_t identity_len = sizeof(TEE_Identity);

    if (!value)
        return TEE_ERROR_BAD_PARAMETERS;

    res = propget_get_property(propsetOrEnumerator, name, &type, value, &identity_len);
    if (type != USER_TA_PROP_TYPE_IDENTITY)
        res = TEE_ERROR_BAD_FORMAT;

    return res;
}
```

#### 7.2.3 OP-TEE风格的枚举器实现示例

```c
/* TEE_AllocatePropertyEnumerator - 参考OP-TEE实现 */
TEE_Result TEE_AllocatePropertyEnumerator(TEE_PropSetHandle* enumerator) {
    struct prop_enumerator* pe;

    if (!enumerator)
        return TEE_ERROR_BAD_PARAMETERS;

    pe = TEE_Malloc(sizeof(struct prop_enumerator),
                    TEE_USER_MEM_HINT_NO_FILL_ZERO);
    if (!pe)
        return TEE_ERROR_OUT_OF_MEMORY;

    *enumerator = (TEE_PropSetHandle)pe;
    TEE_ResetPropertyEnumerator(*enumerator);
    return TEE_SUCCESS;
}

/* TEE_FreePropertyEnumerator - 参考OP-TEE实现 */
void TEE_FreePropertyEnumerator(TEE_PropSetHandle enumerator) {
    struct prop_enumerator* pe = (struct prop_enumerator*)enumerator;
    TEE_Free(pe);
}

/* TEE_StartPropertyEnumerator - 参考OP-TEE实现 */
void TEE_StartPropertyEnumerator(TEE_PropSetHandle enumerator,
                                 TEE_PropSetHandle propSet) {
    struct prop_enumerator* pe = (struct prop_enumerator*)enumerator;

    if (!pe)
        return;

    pe->idx = 0;
    pe->prop_set = propSet;
}

/* TEE_ResetPropertyEnumerator - 参考OP-TEE实现 */
void TEE_ResetPropertyEnumerator(TEE_PropSetHandle enumerator) {
    struct prop_enumerator* pe = (struct prop_enumerator*)enumerator;
    pe->idx = PROP_ENUMERATOR_NOT_STARTED;
}
```

## 8. 测试验证方案

### 8.1 单元测试用例

#### 8.1.1 基本属性获取测试

```c
/* 测试字符串属性获取 */
void test_get_property_as_string(void) {
    char buffer[256];
    size_t buffer_len = sizeof(buffer);
    TEE_Result res;

    res = TEE_GetPropertyAsString(TEE_PROPSET_TEE_IMPLEMENTATION,
                                  "gpd.tee.systemTime.protectionLevel",
                                  buffer, &buffer_len);
    assert(res == TEE_SUCCESS);
    assert(strcmp(buffer, "100") == 0);
}

/* 测试U32属性获取 */
void test_get_property_as_u32(void) {
    uint32_t value;
    TEE_Result res;

    res = TEE_GetPropertyAsU32(TEE_PROPSET_TEE_IMPLEMENTATION,
                               "gpd.tee.systemTime.protectionLevel",
                               &value);
    assert(res == TEE_SUCCESS);
    assert(value == 100);
}
```

#### 8.1.2 属性枚举测试

```c
/* 测试属性枚举器 */
void test_property_enumerator(void) {
    TEE_PropSetHandle enumerator;
    char name_buffer[64];
    size_t name_len;
    TEE_Result res;

    /* 分配枚举器 */
    res = TEE_AllocatePropertyEnumerator(&enumerator);
    assert(res == TEE_SUCCESS);

    /* 启动枚举 */
    res = TEE_StartPropertyEnumerator(enumerator, TEE_PROPSET_TEE_IMPLEMENTATION);
    assert(res == TEE_SUCCESS);

    /* 枚举属性 */
    while (true) {
        name_len = sizeof(name_buffer);
        res = TEE_GetPropertyName(enumerator, name_buffer, &name_len);
        if (res == TEE_ERROR_ITEM_NOT_FOUND)
            break;
        assert(res == TEE_SUCCESS);

        printf("Property: %s\n", name_buffer);

        res = TEE_GetNextProperty(enumerator);
        if (res == TEE_ERROR_ITEM_NOT_FOUND)
            break;
        assert(res == TEE_SUCCESS);
    }

    /* 释放枚举器 */
    TEE_FreePropertyEnumerator(enumerator);
}
```

### 8.2 集成测试

#### 8.2.1 完整API覆盖测试

测试所有12个GP标准属性API的功能正确性，包括：
- 正常情况下的功能验证
- 边界条件处理
- 错误情况处理
- 并发访问安全性

#### 8.2.2 性能测试

- 属性获取延迟测试
- 枚举器性能测试
- 内存使用效率测试
- 并发访问性能测试

## 9. 总结

### 9.1 统一GP标准设计特点

1. **完整GP标准支持**：统一实现全部12个GP标准属性API，无"基础"和"扩展"之分
2. **OP-TEE兼容设计**：属性组织、枚举器结构完全参考OP-TEE，确保设计成熟度
3. **统一数据类型**：所有7种GP属性类型（BOOL、U32、U64、STRING、UUID、IDENTITY、BINARY_BLOCK）统一支持
4. **向后兼容**：保持现有4个API的完全兼容性，平滑升级
5. **线程安全**：所有API都支持并发访问，满足多TA环境需求

### 9.2 关键技术优势

- **统一架构**：三层架构清晰，职责分明，无重复设计
- **类型安全**：严格的类型检查和转换，支持所有GP标准类型
- **混合查找**：本地优先+远程兜底的高效访问策略
- **索引映射**：OP-TEE风格的简洁枚举器，支持跨数据源统一视图
- **TIPC统一**：统一的通信协议，避免命令类型冗余

### 9.3 设计一致性保证

1. **无重复定义**：所有数据类型和API在文档中只定义一次
2. **统一命名**：遵循GP标准命名规范，与OP-TEE保持一致
3. **完整覆盖**：12个API全部按GP标准设计，无遗漏
4. **架构统一**：从数据结构到通信协议，整体设计保持一致性

这个统一的GP标准设计方案为Trusty TEE提供了完整、一致、高效的属性API支持，既符合GP标准规范，又充分利用了OP-TEE的成熟设计经验和Trusty现有的TIPC基础设施。

## 10. 实施指南

### 10.1 GP标准API实施优先级

**第一阶段（高优先级）- 完善属性获取API**：
1. 完善现有数据结构支持所有GP标准属性类型
2. 实现 `TEE_GetPropertyAsBinaryBlock` - 二进制块属性支持
3. 实现 `TEE_GetPropertyAsUUID` - UUID属性支持
4. 实现 `TEE_GetPropertyAsIdentity` - 身份属性支持
5. 扩展TIPC服务支持所有属性类型

**第二阶段（中优先级）- OP-TEE风格枚举器**：
1. 实现OP-TEE风格的 `prop_enumerator` 结构
2. 实现 `TEE_AllocatePropertyEnumerator` - 枚举器分配
3. 实现 `TEE_FreePropertyEnumerator` - 枚举器释放
4. 实现 `TEE_StartPropertyEnumerator` - 枚举器启动
5. 实现 `TEE_ResetPropertyEnumerator` - 枚举器重置

**第三阶段（标准优先级）- 枚举功能完善**：
1. 实现 `TEE_GetPropertyName` - 属性名称获取
2. 实现 `TEE_GetNextProperty` - 枚举器移动
3. 完善本地+远程属性的索引映射机制
4. 完善错误处理和边界检查
5. 完整的单元测试和集成测试

### 10.2 关键文件修改清单

**需要修改的文件**：
1. `user/base/lib/libutee/include/tee_ta_api.h` - 添加新API声明
2. `user/base/lib/libutee/include/user_ta_header.h` - 扩展属性类型枚举
3. `user/base/lib/libutee/include/tee_api_types.h` - 添加新数据结构
4. `user/base/lib/libutee/tee_api_property.c` - 实现新API函数

**需要新增的文件**：
1. `user/base/lib/libutee/include/tee_property_internal.h` - 内部数据结构（prop_enumerator等）

### 10.3 测试策略

**单元测试**：
- 每个新API函数的独立测试
- 边界条件和错误情况测试
- 内存泄漏检测

**集成测试**：
- 与现有属性系统的兼容性测试
- 多线程并发访问测试
- 性能基准测试

**回归测试**：
- 确保现有4个API功能不受影响
- 验证现有TA应用的兼容性

### 10.4 质量保证

**代码审查要点**：
1. 内存管理正确性
2. 线程安全性
3. 错误处理完整性
4. GP标准符合性

**性能要求**：
- 属性获取延迟 < 1ms
- 枚举器内存开销 < 1KB
- 支持并发访问数 > 10



### 4.3 TIPC通用TA服务实现（内核侧）

#### 4.3.1 内核属性数据库

```c
/* 内核侧属性数据库 - 扩展OP-TEE的属性集 */
static const struct user_ta_property kernel_tee_props[] = {
    {
        "gpd.tee.arith.maxBigIntSize",
        USER_TA_PROP_TYPE_U32,
        &(const uint32_t){CFG_TA_BIGNUM_MAX_BITS}
    },
    {
        "gpd.tee.systemTime.protectionLevel",
        USER_TA_PROP_TYPE_U32,
        &(const uint32_t){100}
    },
    {
        "gpd.tee.TAPersistentTime.protectionLevel",
        USER_TA_PROP_TYPE_U32,
        &(const uint32_t){1000}
    },
    {
        "gpd.tee.internalCore.version",
        USER_TA_PROP_TYPE_U32,
        &(const uint32_t){TEE_CORE_API_VERSION}
    },
    /* Trusty特有属性 */
    {
        "trusty.tee.version",
        USER_TA_PROP_TYPE_STRING,
        "1.0.0"
    },
    {
        "trusty.tee.tipc.version",
        USER_TA_PROP_TYPE_U32,
        &(const uint32_t){1}
    }
};

/* 动态客户端属性获取 */
static TEE_Result get_client_properties(uint32_t client_id,
                                         const char* prop_name,
                                         struct property_response* resp) {
    /* 根据客户端ID获取相应属性 */
    if (!strcmp(prop_name, "gpd.client.identity")) {
        resp->prop_type = USER_TA_PROP_TYPE_IDENTITY;
        resp->identity_value.login = TEE_LOGIN_APPLICATION;
        /* 填充客户端UUID */
        return TEE_SUCCESS;
    }

    return TEE_ERROR_ITEM_NOT_FOUND;
}
```

#### 4.2.2 TIPC服务处理函数

```c
/* 内核属性复制函数 */
static TEE_Result copy_property_to_response(const struct user_ta_property* prop,
                                           struct property_response* resp) {
    resp->result = TEE_SUCCESS;
    resp->prop_type = prop->type;
    strncpy(resp->name, prop->name, sizeof(resp->name) - 1);

    switch (prop->type) {
    case USER_TA_PROP_TYPE_BOOL:
        resp->bool_value = *(bool*)prop->value;
        resp->value_len = sizeof(bool);
        break;
    case USER_TA_PROP_TYPE_U32:
        resp->u32_value = *(uint32_t*)prop->value;
        resp->value_len = sizeof(uint32_t);
        break;
    case USER_TA_PROP_TYPE_U64:
        resp->u64_value = *(uint64_t*)prop->value;
        resp->value_len = sizeof(uint64_t);
        break;
    case USER_TA_PROP_TYPE_STRING:
        strncpy(resp->str_value, (char*)prop->value, sizeof(resp->str_value) - 1);
        resp->value_len = strlen(resp->str_value) + 1;
        break;
    case USER_TA_PROP_TYPE_UUID:
        memcpy(&resp->uuid_value, prop->value, sizeof(TEE_UUID));
        resp->value_len = sizeof(TEE_UUID);
        break;
    case USER_TA_PROP_TYPE_IDENTITY:
        memcpy(&resp->identity_value, prop->value, sizeof(TEE_Identity));
        resp->value_len = sizeof(TEE_Identity);
        break;
    case USER_TA_PROP_TYPE_BINARY_BLOCK:
        struct binary_property* bp = (struct binary_property*)prop->value;
        size_t copy_len = min(bp->length, sizeof(resp->binary_value));
        memcpy(resp->binary_value, bp->data, copy_len);
        resp->value_len = bp->length;
        if (bp->length > sizeof(resp->binary_value))
            return TEE_ERROR_SHORT_BUFFER;
        break;
    default:
        return TEE_ERROR_BAD_FORMAT;
    }

    return TEE_SUCCESS;
}

/* 客户端属性枚举函数 */
static TEE_Result enumerate_client_properties(struct property_enum_request* req,
                                             struct property_enum_response* resp) {
    static const char* client_prop_names[] = {
        "gpd.client.identity",
        "gpd.client.login",
        "gpd.client.uuid"
    };
    static const uint32_t client_prop_types[] = {
        USER_TA_PROP_TYPE_IDENTITY,
        USER_TA_PROP_TYPE_U32,
        USER_TA_PROP_TYPE_UUID
    };

    size_t total_props = ARRAY_SIZE(client_prop_names);
    size_t start_idx = req->start_index;
    size_t count = 0;
    size_t i;

    resp->total_count = total_props;

    for (i = start_idx; i < total_props && count < req->count &&
         count < ARRAY_SIZE(resp->properties); i++, count++) {
        strncpy(resp->properties[count].name, client_prop_names[i],
                sizeof(resp->properties[count].name) - 1);
        resp->properties[count].type = client_prop_types[i];
    }

    resp->returned_count = count;
    resp->result = TEE_SUCCESS;
    return TEE_SUCCESS;
}

/* 处理属性查找请求 */
static int handle_property_request(struct property_request* req,
                                   struct property_response* resp) {
    const struct user_ta_property* props = NULL;
    size_t props_len = 0;
    size_t n;

    /* 根据属性集选择数据源 */
    switch (req->prop_set) {
    case TEE_PROPSET_TEE_IMPLEMENTATION:
        props = kernel_tee_props;
        props_len = ARRAY_SIZE(kernel_tee_props);
        break;
    case TEE_PROPSET_CURRENT_CLIENT:
        return get_client_properties(get_current_client_id(),
                                     req->by_name.name, resp);
    case TEE_PROPSET_CURRENT_TA:
        /* TA属性通常在用户空间处理 */
        return TEE_ERROR_ITEM_NOT_FOUND;
    default:
        return TEE_ERROR_BAD_PARAMETERS;
    }

    /* 在属性数组中查找 */
    for (n = 0; n < props_len; n++) {
        if (!strcmp(req->by_name.name, props[n].name)) {
            return copy_property_to_response(&props[n], resp);
        }
    }

    return TEE_ERROR_ITEM_NOT_FOUND;
}

/* 处理属性枚举请求 */
static int handle_property_enum_request(struct property_enum_request* req,
                                         struct property_enum_response* resp) {
    const struct user_ta_property* props = NULL;
    size_t props_len = 0;
    size_t start_idx = req->start_index;
    size_t count = 0;
    size_t i;

    /* 获取属性集 */
    switch (req->prop_set) {
    case TEE_PROPSET_TEE_IMPLEMENTATION:
        props = kernel_tee_props;
        props_len = ARRAY_SIZE(kernel_tee_props);
        break;
    case TEE_PROPSET_CURRENT_CLIENT:
        /* 客户端属性动态生成 */
        return enumerate_client_properties(req, resp);
    default:
        return TEE_ERROR_BAD_PARAMETERS;
    }

    resp->total_count = props_len;

    /* 复制属性信息 */
    for (i = start_idx; i < props_len && count < req->count &&
         count < ARRAY_SIZE(resp->properties); i++, count++) {
        strncpy(resp->properties[count].name, props[i].name,
                sizeof(resp->properties[count].name) - 1);
        resp->properties[count].type = props[i].type;
    }

    resp->returned_count = count;
    return TEE_SUCCESS;
}

/* 处理按索引获取属性请求 */
static int handle_property_by_index_request(struct property_request* req,
                                           struct property_response* resp) {
    const struct user_ta_property* props = NULL;
    size_t props_len = 0;
    size_t index = req->by_index.index;

    /* 根据属性集选择数据源 */
    switch (req->prop_set) {
    case TEE_PROPSET_TEE_IMPLEMENTATION:
        props = kernel_tee_props;
        props_len = ARRAY_SIZE(kernel_tee_props);
        break;
    case TEE_PROPSET_CURRENT_CLIENT:
        /* 客户端属性按索引获取 */
        return get_client_property_by_index(index, resp);
    case TEE_PROPSET_CURRENT_TA:
        /* TA属性通常在用户空间处理 */
        return TEE_ERROR_ITEM_NOT_FOUND;
    default:
        return TEE_ERROR_BAD_PARAMETERS;
    }

    /* 检查索引范围 */
    if (index >= props_len)
        return TEE_ERROR_ITEM_NOT_FOUND;

    return copy_property_to_response(&props[index], resp);
}

/* 处理获取属性数量请求 */
static int handle_property_count_request(struct property_request* req,
                                        struct property_response* resp) {
    size_t count = 0;

    switch (req->prop_set) {
    case TEE_PROPSET_TEE_IMPLEMENTATION:
        count = ARRAY_SIZE(kernel_tee_props);
        break;
    case TEE_PROPSET_CURRENT_CLIENT:
        count = 3; /* gpd.client.identity, gpd.client.login, gpd.client.uuid */
        break;
    case TEE_PROPSET_CURRENT_TA:
        count = 0; /* TA属性在用户空间处理 */
        break;
    default:
        return TEE_ERROR_BAD_PARAMETERS;
    }

    resp->result = TEE_SUCCESS;
    resp->prop_type = USER_TA_PROP_TYPE_U32;
    resp->u32_value = count;
    resp->value_len = sizeof(uint32_t);
    return TEE_SUCCESS;
}

/* 统一的TIPC命令分发器 */
static int handle_generic_ta_command(uint32_t cmd,
                                    void* req_buf, size_t req_size,
                                    void* resp_buf, size_t resp_size) {
    switch (cmd) {
    case GENERIC_TA_CMD_GET_PROPERTY_BY_NAME:
        if (req_size < sizeof(struct property_request) ||
            resp_size < sizeof(struct property_response))
            return TEE_ERROR_BAD_PARAMETERS;
        return handle_property_request((struct property_request*)req_buf,
                                     (struct property_response*)resp_buf);

    case GENERIC_TA_CMD_GET_PROPERTY_BY_INDEX:
        if (req_size < sizeof(struct property_request) ||
            resp_size < sizeof(struct property_response))
            return TEE_ERROR_BAD_PARAMETERS;
        return handle_property_by_index_request((struct property_request*)req_buf,
                                              (struct property_response*)resp_buf);

    case GENERIC_TA_CMD_ENUM_PROPERTIES:
        if (req_size < sizeof(struct property_enum_request) ||
            resp_size < sizeof(struct property_enum_response))
            return TEE_ERROR_BAD_PARAMETERS;
        return handle_property_enum_request((struct property_enum_request*)req_buf,
                                          (struct property_enum_response*)resp_buf);

    case GENERIC_TA_CMD_GET_PROPERTY_COUNT:
        if (req_size < sizeof(struct property_request) ||
            resp_size < sizeof(struct property_response))
            return TEE_ERROR_BAD_PARAMETERS;
        return handle_property_count_request((struct property_request*)req_buf,
                                           (struct property_response*)resp_buf);

    /* 兼容现有的分类型命令 */
    case GENERIC_TA_CMD_GET_PROPERTY_STRING:
    case GENERIC_TA_CMD_GET_PROPERTY_BOOL:
    case GENERIC_TA_CMD_GET_PROPERTY_U32:
    case GENERIC_TA_CMD_GET_PROPERTY_U64:
        /* 转换为统一的按名称获取命令 */
        return handle_legacy_property_command(cmd, req_buf, req_size,
                                            resp_buf, resp_size);

    default:
        return TEE_ERROR_NOT_SUPPORTED;
    }
}
```


