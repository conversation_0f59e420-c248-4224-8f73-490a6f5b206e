# Trusty TEE GP标准属性API数据结构统计文档

## 1. 概述

本文档统计Trusty TEE GP标准属性API设计中所需的所有数据结构，按功能分类并提供详细的字段说明和使用场景。

## 2. 数据结构分类统计

### 2.1 总体统计

| 分类 | 数量 | 说明 |
|------|------|------|
| **GP标准结构** | 2个 | TEE_UUID、TEE_Identity |
| **OP-TEE风格结构** | 2个 | prop_enumerator、binary_property |
| **TIPC协议结构** | 4个 | property_request、property_response、property_enum_request、property_enum_response |
| **枚举和常量** | 2个 | user_ta_prop_type、generic_ta_cmd |
| **总计** | **10个** | 完整的GP标准属性API数据结构体系 |

### 2.2 按使用层次分类

| 层次 | 结构数量 | 具体结构 |
|------|----------|----------|
| **用户API层** | 2个 | TEE_UUID、TEE_Identity |
| **属性管理层** | 2个 | prop_enumerator、binary_property |
| **TIPC通信层** | 4个 | property_request、property_response、property_enum_request、property_enum_response |
| **系统定义层** | 2个 | user_ta_prop_type、generic_ta_cmd |

## 3. GP标准结构（2个）

### 3.1 TEE_UUID - GP标准UUID结构

```c
typedef struct {
    uint32_t timeLow;                    /* 时间戳低32位 */
    uint16_t timeMid;                    /* 时间戳中16位 */
    uint16_t timeHiAndVersion;           /* 时间戳高16位和版本 */
    uint8_t clockSeqAndNode[8];          /* 时钟序列和节点标识 */
} TEE_UUID;
```

**字段说明**：
- `timeLow`: UUID时间戳的低32位
- `timeMid`: UUID时间戳的中16位  
- `timeHiAndVersion`: UUID时间戳的高16位和版本信息
- `clockSeqAndNode`: 8字节的时钟序列和节点标识

**使用场景**：
- TA身份标识
- 客户端UUID属性
- 系统组件标识

### 3.2 TEE_Identity - GP标准身份结构

```c
typedef struct {
    uint32_t login;                      /* 登录类型 */
    TEE_UUID uuid;                       /* 身份UUID */
} TEE_Identity;
```

**字段说明**：
- `login`: 登录类型（TEE_LOGIN_PUBLIC、TEE_LOGIN_USER等）
- `uuid`: 身份对应的UUID

**使用场景**：
- 客户端身份属性
- 访问控制验证
- 安全上下文管理

## 4. OP-TEE风格结构（2个）

### 4.1 prop_enumerator - OP-TEE风格枚举器

```c
#define PROP_ENUMERATOR_NOT_STARTED ((size_t)-1)

struct prop_enumerator {
    size_t idx;                          /* 当前索引位置 */
    TEE_PropSetHandle prop_set;          /* 关联的属性集句柄 */
};
```

**注意**：设计文档中存在两个版本的定义，推荐使用 `size_t idx` 版本以支持更大的索引范围。

**字段说明**：
- `idx`: 当前枚举位置索引，支持本地+远程属性的统一索引
- `prop_set`: 关联的属性集句柄（TEE_PROPSET_*）

**设计特点**：
- 简洁设计：只有2个核心字段
- 索引映射：idx < eps_len为本地属性，idx >= eps_len为远程属性
- OP-TEE兼容：完全参考OP-TEE的枚举器设计

**使用场景**：
- 属性枚举遍历
- 本地+远程属性统一访问
- 枚举器状态管理

### 4.2 binary_property - 二进制块属性

```c
struct binary_property {
    const uint8_t* data;                 /* 二进制数据指针 */
    size_t length;                       /* 数据长度 */
};
```

**字段说明**：
- `data`: 指向二进制数据的指针
- `length`: 二进制数据的字节长度

**使用场景**：
- 二进制块属性存储
- 证书和密钥数据
- 配置文件数据

## 5. TIPC协议结构（4个）

### 5.1 property_request - 统一属性请求

```c
struct property_request {
    struct generic_ta_msg_hdr hdr;       /* 消息头 */
    uint32_t prop_set;                   /* 属性集类型 */
    union {
        struct {
            char name[64];               /* 按名称查找 */
        } by_name;
        struct {
            uint32_t index;              /* 按索引查找 */
        } by_index;
    };
};
```

**字段说明**：
- `hdr`: TIPC消息头，包含命令类型等信息
- `prop_set`: 属性集类型（TEE_PROPSET_*）
- `by_name.name`: 属性名称（按名称查找时使用）
- `by_index.index`: 属性索引（按索引查找时使用）

**使用场景**：
- TIPC属性查找请求
- 支持按名称和按索引两种查找方式
- 统一的请求格式

### 5.2 property_response - 统一属性响应

```c
struct property_response {
    int32_t result;                      /* 操作结果 */
    uint32_t prop_type;                  /* 属性类型 */
    char name[64];                       /* 属性名称 */
    union {
        char str_value[256];             /* 字符串值 */
        bool bool_value;                 /* 布尔值 */
        uint32_t u32_value;              /* 32位整数值 */
        uint64_t u64_value;              /* 64位整数值 */
        uint8_t binary_value[256];       /* 二进制值 */
        TEE_UUID uuid_value;             /* UUID值 */
        TEE_Identity identity_value;     /* 身份值 */
    };
    uint32_t value_len;                  /* 值长度 */
};
```

**字段说明**：
- `result`: 操作结果码
- `prop_type`: 属性类型（user_ta_prop_type枚举值）
- `name`: 属性名称
- `union`: 属性值联合体，支持所有7种GP标准类型
- `value_len`: 实际值长度

**使用场景**：
- TIPC属性查找响应
- 支持所有GP标准属性类型
- 统一的响应格式

### 5.3 property_enum_request - 枚举请求

```c
struct property_enum_request {
    struct generic_ta_msg_hdr hdr;       /* 消息头 */
    uint32_t prop_set;                   /* 属性集类型 */
    uint32_t start_index;                /* 起始索引 */
    uint32_t count;                      /* 请求数量 */
};
```

**字段说明**：
- `hdr`: TIPC消息头，包含命令类型等信息
- `prop_set`: 属性集类型
- `start_index`: 枚举起始索引
- `count`: 请求返回的属性数量

**使用场景**：
- 批量属性枚举请求
- 分页属性查询
- 枚举器远程属性访问

### 5.4 property_enum_response - 枚举响应

```c
struct property_info {
    char name[64];                       /* 属性名称 */
    uint32_t type;                       /* 属性类型 */
};

struct property_enum_response {
    int32_t result;                      /* 操作结果 */
    uint32_t total_count;                /* 总属性数量 */
    uint32_t returned_count;             /* 返回的属性数量 */
    struct property_info properties[16]; /* 属性信息数组 */
};
```

**字段说明**：
- `result`: 操作结果码
- `total_count`: 属性集中的总属性数量
- `returned_count`: 本次返回的属性数量
- `properties`: 属性信息数组

**使用场景**：
- 批量属性枚举响应
- 属性列表信息返回
- 枚举器状态同步

## 6. 枚举和常量（2个）

### 6.1 user_ta_prop_type - GP标准属性类型

```c
enum user_ta_prop_type {
    USER_TA_PROP_TYPE_BOOL,              /* bool */
    USER_TA_PROP_TYPE_U32,               /* uint32_t */
    USER_TA_PROP_TYPE_STRING,            /* zero terminated string of char */
    USER_TA_PROP_TYPE_U64,               /* uint64_t */
    USER_TA_PROP_TYPE_UUID,              /* TEE_UUID */
    USER_TA_PROP_TYPE_IDENTITY,          /* TEE_Identity */
    USER_TA_PROP_TYPE_BINARY_BLOCK,      /* binary block */
    USER_TA_PROP_TYPE_INVALID,           /* invalid value */
};
```

**使用场景**：
- 属性类型标识
- 类型检查和转换
- TIPC协议中的类型字段

### 6.2 generic_ta_cmd - 统一TIPC命令

```c
enum generic_ta_cmd {
    GENERIC_TA_CMD_INVALID = 0,
    
    /* 统一属性获取命令 - GP标准 */
    GENERIC_TA_CMD_GET_PROPERTY_BY_NAME = 1,    /* 按名称获取属性 */
    GENERIC_TA_CMD_GET_PROPERTY_BY_INDEX = 2,   /* 按索引获取属性 */
    
    /* 属性枚举命令 - GP标准 */
    GENERIC_TA_CMD_ENUM_PROPERTIES = 3,         /* 枚举属性列表 */
    GENERIC_TA_CMD_GET_PROPERTY_COUNT = 4,      /* 获取属性数量 */
    
    /* 兼容现有命令 - 保持向后兼容 */
    GENERIC_TA_CMD_GET_PROPERTY_STRING = 10,
    GENERIC_TA_CMD_GET_PROPERTY_BOOL = 11,
    GENERIC_TA_CMD_GET_PROPERTY_U32 = 12,
    GENERIC_TA_CMD_GET_PROPERTY_U64 = 13,
};
```

**使用场景**：
- TIPC命令类型标识
- 命令分发和处理
- 协议版本兼容

## 7. 数据结构关系图

```
GP标准API层
├── TEE_UUID (GP标准)
├── TEE_Identity (GP标准)
└── user_ta_prop_type (类型枚举)

属性管理层  
├── prop_enumerator (OP-TEE风格)
└── binary_property (二进制块)

TIPC通信层
├── property_request (统一请求)
├── property_response (统一响应)  
├── property_enum_request (枚举请求)
├── property_enum_response (枚举响应)
└── generic_ta_cmd (命令枚举)
```

## 8. 内存使用统计

| 结构名称 | 大小估算 | 说明 |
|----------|----------|------|
| TEE_UUID | 16字节 | 固定大小 |
| TEE_Identity | 20字节 | 包含UUID |
| prop_enumerator | 16字节 | 2个指针/整数 |
| binary_property | 16字节 | 指针+长度 |
| property_request | 80字节+ | 包含消息头+64字节名称 |
| property_response | 340字节 | 包含各类型联合体 |
| property_enum_request | 24字节+ | 包含消息头+3个32位整数 |
| property_enum_response | 1100字节+ | 包含16个属性信息 |

**总计**：约1.6KB的核心数据结构，支持完整的GP标准属性API功能。

## 9. 数据结构一致性确认

### 9.1 与设计文档的一致性检查

✅ **已确认一致**：
- 所有数据结构定义与主设计文档完全一致
- 字段类型和命名规范统一
- 消息头字段已补充完整
- 枚举器索引类型统一为 `size_t`

### 9.2 修正的不一致问题

1. **prop_enumerator.idx字段类型**：统一为 `size_t` 以支持更大索引范围
2. **TIPC消息结构**：补充了 `generic_ta_msg_hdr` 消息头字段
3. **常量定义**：统一使用 `((size_t)-1)` 作为未启动状态

## 10. 总结

本数据结构体系具有以下特点：

1. **完整性**：覆盖GP标准的所有属性类型和操作
2. **一致性**：与主设计文档完全一致，统一的设计风格和命名规范
3. **兼容性**：与OP-TEE设计保持兼容
4. **高效性**：合理的内存使用和通信开销
5. **扩展性**：支持未来的功能扩展

这些数据结构为Trusty TEE提供了完整、高效、可靠的GP标准属性API基础设施。
