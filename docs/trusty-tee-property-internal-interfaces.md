# Trusty TEE GP标准属性API内部接口文档

## 1. 概述

本文档整理Trusty TEE GP标准属性API设计中的所有内部接口，包括属性管理层、TIPC服务层和辅助函数的详细接口规范。基于OP-TEE的成熟设计，实现完整的12个GP标准属性API。

### 1.1 接口分类统计

**GP标准API（12个）**：
- 属性获取API：7个（String、Bool、U32、U64、BinaryBlock、UUID、Identity）
- 属性枚举API：5个（Allocate、Free、Start、Reset、GetPropertyName、GetNextProperty）

**内部接口（23个）**：
- 核心管理接口：3个
- TIPC服务接口：6个
- 辅助函数接口：7个
- 内核服务接口：7个

**数据结构（8个）**：
- OP-TEE风格结构：2个
- TIPC协议结构：4个
- GP标准结构：2个

## 2. 属性管理层接口

### 2.1 核心属性获取接口

| 项目 | 内容 |
|------|------|
| 函数接口 | `static TEE_Result propget_get_property(TEE_PropSetHandle h, const char* name, enum user_ta_prop_type* type, void* buf, uint32_t* len)` |
| 输入 | h: 属性集句柄，可以是伪句柄(TEE_PROPSET_*)或枚举器句柄<br>name: 属性名称字符串指针<br>type: 指向属性类型枚举的指针，用于输入期望类型和输出实际类型<br>buf: 属性值缓冲区指针<br>len: 指向缓冲区长度的指针，输入缓冲区大小，输出实际长度 |
| 输出 | type: 返回实际的属性类型<br>buf: 填充属性值数据<br>len: 返回实际的属性值长度 |
| 返回值 | TEE_SUCCESS: 成功获取属性<br>TEE_ERROR_ITEM_NOT_FOUND: 属性未找到<br>TEE_ERROR_SHORT_BUFFER: 缓冲区长度不足<br>TEE_ERROR_COMMUNICATION: TIPC通信失败<br>TEE_ERROR_BAD_PARAMETERS: 参数无效 |
| 说明 | 统一属性获取入口，实现本地优先、远程兜底的智能路由机制 |

| 项目 | 内容 |
|------|------|
| 函数接口 | `static TEE_Result propset_get(TEE_PropSetHandle h, const struct user_ta_property** eps, size_t* eps_len)` |
| 输入 | h: 属性集句柄(TEE_PROPSET_CURRENT_TA、TEE_PROPSET_CURRENT_CLIENT、TEE_PROPSET_TEE_IMPLEMENTATION) |
| 输出 | eps: 返回指向属性数组的指针<br>eps_len: 返回属性数组的长度 |
| 返回值 | TEE_SUCCESS: 成功获取属性集<br>TEE_ERROR_ITEM_NOT_FOUND: 无效的属性集句柄 |
| 说明 | 根据属性集句柄获取对应的本地属性数组，参考OP-TEE的propset_get函数 |

| 项目 | 内容 |
|------|------|
| 函数接口 | `static TEE_Result propget_get_ext_prop(const struct user_ta_property* prop, enum user_ta_prop_type* type, void* buf, uint32_t* len)` |
| 输入 | prop: 指向属性结构体的指针<br>type: 指向属性类型枚举的指针<br>buf: 属性值缓冲区指针<br>len: 指向缓冲区长度的指针 |
| 输出 | type: 返回属性的实际类型<br>buf: 填充属性值数据<br>len: 返回实际的属性值长度 |
| 返回值 | TEE_SUCCESS: 成功提取属性值<br>TEE_ERROR_SHORT_BUFFER: 缓冲区长度不足<br>TEE_ERROR_BAD_FORMAT: 不支持的属性类型 |
| 说明 | 从属性结构体中提取属性值，支持所有7种GP标准类型（BOOL、U32、U64、STRING、UUID、IDENTITY、BINARY_BLOCK） |

| 项目 | 内容 |
|------|------|
| 函数接口 | `static bool is_propset_pseudo_handle(TEE_PropSetHandle h)` |
| 输入 | h: 属性集句柄 |
| 输出 | 无 |
| 返回值 | true: 是伪句柄(TEE_PROPSET_*)<br>false: 不是伪句柄(可能是枚举器句柄) |
| 说明 | OP-TEE风格的句柄判断函数，用于区分属性集句柄和枚举器句柄 |

### 2.2 枚举器管理接口（OP-TEE风格）

基于OP-TEE的简洁枚举器设计，枚举器直接使用 `TEE_PropSetHandle` 类型，内部为 `struct prop_enumerator` 结构。

**实现状态**：
- ✅ 已实现：基础属性获取API（4个）- String、Bool、U32、U64
- 📋 待实现：扩展属性获取API（3个）- BinaryBlock、UUID、Identity
- 📋 待实现：属性枚举器API（5个）- Allocate、Free、Start、Reset、GetPropertyName、GetNextProperty

**核心特点**：
- 采用OP-TEE的简洁设计：只有 `idx` 和 `prop_set` 两个字段
- 支持本地+远程属性的统一索引映射
- 兼容OP-TEE的枚举器使用模式

## 3. TIPC服务层接口

### 3.1 属性查找服务接口

| 项目 | 内容 |
|------|------|
| 函数接口 | `static TEE_Result get_property_from_tipc_service(TEE_PropSetHandle prop_set, const char* name, enum user_ta_prop_type* type, void* buf, uint32_t* len)` |
| 输入 | prop_set: 属性集句柄<br>name: 属性名称字符串<br>type: 指向期望属性类型的指针<br>buf: 属性值缓冲区指针<br>len: 指向缓冲区长度的指针 |
| 输出 | type: 返回实际的属性类型<br>buf: 填充属性值数据<br>len: 返回实际的属性值长度 |
| 返回值 | TEE_SUCCESS: 成功获取属性<br>TEE_ERROR_COMMUNICATION: TIPC通信失败<br>TEE_ERROR_ITEM_NOT_FOUND: 属性未找到<br>TEE_ERROR_SHORT_BUFFER: 缓冲区长度不足 |
| 说明 | 通过TIPC服务从内核空间获取属性值，处理连接建立、消息收发和响应解析 |

| 项目 | 内容 |
|------|------|
| 函数接口 | `static TEE_Result get_property_by_index_from_tipc_service(TEE_PropSetHandle prop_set, uint32_t index, char* name, enum user_ta_prop_type* type, void* buf, uint32_t* len)` |
| 输入 | prop_set: 属性集句柄<br>index: 属性索引(相对于远程属性的起始位置)<br>name: 属性名称缓冲区指针<br>type: 指向属性类型的指针<br>buf: 属性值缓冲区指针<br>len: 指向缓冲区长度的指针 |
| 输出 | name: 返回属性名称<br>type: 返回属性类型<br>buf: 填充属性值数据<br>len: 返回实际的属性值长度 |
| 返回值 | TEE_SUCCESS: 成功获取属性<br>TEE_ERROR_COMMUNICATION: TIPC通信失败<br>TEE_ERROR_ITEM_NOT_FOUND: 索引超出范围<br>TEE_ERROR_SHORT_BUFFER: 缓冲区长度不足 |
| 说明 | 通过索引从TIPC服务获取属性信息，主要用于枚举器的远程属性访问 |

| 项目 | 内容 |
|------|------|
| 函数接口 | `static TEE_Result copy_property_value_to_buffer(struct property_response* resp, enum user_ta_prop_type* type, void* buf, uint32_t* len)` |
| 输入 | resp: 指向TIPC响应消息的指针<br>type: 指向属性类型的指针<br>buf: 目标缓冲区指针<br>len: 指向缓冲区长度的指针 |
| 输出 | type: 返回属性的实际类型<br>buf: 填充属性值数据<br>len: 返回实际的属性值长度 |
| 返回值 | TEE_SUCCESS: 成功复制属性值<br>TEE_ERROR_SHORT_BUFFER: 缓冲区长度不足<br>TEE_ERROR_BAD_FORMAT: 属性类型不匹配 |
| 说明 | 从TIPC响应消息中提取属性值并复制到用户缓冲区，支持所有7种GP标准类型 |

| 项目 | 内容 |
|------|------|
| 函数接口 | `static TEE_Result sys_to_tee_error(int sys_error)` |
| 输入 | sys_error: 系统错误码(如TIPC错误码) |
| 输出 | 无 |
| 返回值 | 对应的TEE_Result错误码 |
| 说明 | 将系统级错误码转换为TEE标准错误码 |

### 3.2 属性枚举服务接口

| 项目 | 内容 |
|------|------|
| 函数接口 | `static TEE_Result get_property_name_from_tipc_service(TEE_PropSetHandle prop_set, uint32_t index, void* nameBuffer, size_t* nameBufferLen)` |
| 输入 | prop_set: 属性集句柄<br>index: 属性索引(相对于远程属性的起始位置)<br>nameBuffer: 属性名称缓冲区指针<br>nameBufferLen: 指向缓冲区长度的指针 |
| 输出 | nameBuffer: 填充属性名称<br>nameBufferLen: 返回实际的名称长度 |
| 返回值 | TEE_SUCCESS: 成功获取属性名称<br>TEE_ERROR_COMMUNICATION: TIPC通信失败<br>TEE_ERROR_ITEM_NOT_FOUND: 索引超出范围<br>TEE_ERROR_SHORT_BUFFER: 缓冲区长度不足 |
| 说明 | 从TIPC服务获取指定索引的属性名称，用于枚举器的TEE_GetPropertyName实现（预留接口，当前实现暂不支持） |

| 项目 | 内容 |
|------|------|
| 函数接口 | `static TEE_Result check_tipc_property_exists(TEE_PropSetHandle prop_set, uint32_t index)` |
| 输入 | prop_set: 属性集句柄<br>index: 属性索引(相对于远程属性的起始位置) |
| 输出 | 无 |
| 返回值 | TEE_SUCCESS: 指定索引的属性存在<br>TEE_ERROR_ITEM_NOT_FOUND: 指定索引的属性不存在或索引超出范围<br>TEE_ERROR_COMMUNICATION: TIPC通信失败 |
| 说明 | 检查TIPC服务中是否存在指定索引的属性，用于枚举器的TEE_GetNextProperty实现（预留接口，当前实现暂不支持） |

## 4. 辅助函数接口

### 4.1 属性值处理接口

| 项目 | 内容 |
|------|------|
| 函数接口 | `static TEE_Result copy_property_value_to_buffer(const struct property_response* resp, enum user_ta_prop_type* type, void* buf, uint32_t* len)` |
| 输入 | resp: 指向TIPC响应消息的指针<br>type: 指向属性类型的指针<br>buf: 目标缓冲区指针<br>len: 指向缓冲区长度的指针 |
| 输出 | type: 返回属性的实际类型<br>buf: 填充属性值数据<br>len: 返回实际的属性值长度 |
| 返回值 | TEE_SUCCESS: 成功复制属性值<br>TEE_ERROR_SHORT_BUFFER: 缓冲区长度不足<br>TEE_ERROR_BAD_FORMAT: 属性类型不匹配 |
| 说明 | 从TIPC响应消息中提取属性值并复制到用户缓冲区，处理类型检查和长度验证 |

### 4.2 句柄检查接口

| 项目 | 内容 |
|------|------|
| 函数接口 | `static bool is_propset_pseudo_handle(TEE_PropSetHandle h)` |
| 输入 | h: 属性集句柄 |
| 输出 | 无 |
| 返回值 | true: 是伪句柄(TEE_PROPSET_*)<br>false: 不是伪句柄(可能是枚举器句柄) |
| 说明 | 检查句柄是否为预定义的伪句柄，用于区分属性集句柄和枚举器句柄 |

## 5. 数据结构定义统计

### 5.1 OP-TEE风格结构（2个）

**struct prop_enumerator - OP-TEE风格枚举器**：
```c
#define PROP_ENUMERATOR_NOT_STARTED ((size_t)-1)

struct prop_enumerator {
    size_t idx;                          /* 当前索引位置 */
    TEE_PropSetHandle prop_set;          /* 关联的属性集句柄 */
};
```

**struct binary_property - 二进制块属性**：
```c
struct binary_property {
    const uint8_t* data;                 /* 二进制数据指针 */
    size_t length;                       /* 数据长度 */
};
```

### 5.2 TIPC协议结构（4个）

**property_request - 统一属性请求**：
```c
struct property_request {
    uint32_t prop_set;                   /* 属性集类型 */
    union {
        struct {
            char name[64];               /* 按名称查找 */
        } by_name;
        struct {
            uint32_t index;              /* 按索引查找 */
        } by_index;
    };
};
```

**property_response - 统一属性响应**：
```c
struct property_response {
    int32_t result;                      /* 操作结果 */
    uint32_t prop_type;                  /* 属性类型 */
    char name[64];                       /* 属性名称 */
    union {
        char str_value[256];             /* 字符串值 */
        bool bool_value;                 /* 布尔值 */
        uint32_t u32_value;              /* 32位整数值 */
        uint64_t u64_value;              /* 64位整数值 */
        uint8_t binary_value[256];       /* 二进制值 */
        TEE_UUID uuid_value;             /* UUID值 */
        TEE_Identity identity_value;     /* 身份值 */
    };
    uint32_t value_len;                  /* 值长度 */
};
```

**property_enum_request - 枚举请求**：
```c
struct property_enum_request {
    uint32_t prop_set;                   /* 属性集类型 */
    uint32_t start_index;                /* 起始索引 */
    uint32_t count;                      /* 请求数量 */
};
```

**property_enum_response - 枚举响应**：
```c
struct property_info {
    char name[64];                       /* 属性名称 */
    uint32_t type;                       /* 属性类型 */
};

struct property_enum_response {
    int32_t result;                      /* 操作结果 */
    uint32_t total_count;                /* 总属性数量 */
    uint32_t returned_count;             /* 返回的属性数量 */
    struct property_info properties[16]; /* 属性信息数组 */
};
```

### 5.3 GP标准结构（2个）

**TEE_UUID - GP标准UUID结构**：
```c
typedef struct {
    uint32_t timeLow;
    uint16_t timeMid;
    uint16_t timeHiAndVersion;
    uint8_t clockSeqAndNode[8];
} TEE_UUID;
```

**TEE_Identity - GP标准身份结构**：
```c
typedef struct {
    uint32_t login;                      /* 登录类型 */
    TEE_UUID uuid;                       /* 身份UUID */
} TEE_Identity;
```

## 6. 常量定义

### 6.1 枚举器常量
```c
#define PROP_ENUMERATOR_NOT_STARTED     ((size_t)-1)
```

### 6.2 TIPC服务常量
```c
#define GENERIC_TA_PORT                 "com.android.trusty.generic_ta"
```

### 6.3 GP标准属性类型
```c
enum user_ta_prop_type {
    USER_TA_PROP_TYPE_BOOL,              /* bool */
    USER_TA_PROP_TYPE_U32,               /* uint32_t */
    USER_TA_PROP_TYPE_STRING,            /* zero terminated string of char */
    USER_TA_PROP_TYPE_U64,               /* uint64_t */
    USER_TA_PROP_TYPE_UUID,              /* TEE_UUID */
    USER_TA_PROP_TYPE_IDENTITY,          /* TEE_Identity */
    USER_TA_PROP_TYPE_BINARY_BLOCK,      /* binary block */
    USER_TA_PROP_TYPE_INVALID,           /* invalid value */
};
```

### 6.4 统一TIPC命令（GP标准）
```c
enum generic_ta_cmd {
    GENERIC_TA_CMD_INVALID = 0,

    /* 统一属性获取命令 - GP标准 */
    GENERIC_TA_CMD_GET_PROPERTY_BY_NAME = 1,    /* 按名称获取属性 */
    GENERIC_TA_CMD_GET_PROPERTY_BY_INDEX = 2,   /* 按索引获取属性 */

    /* 属性枚举命令 - GP标准 */
    GENERIC_TA_CMD_ENUM_PROPERTIES = 3,         /* 枚举属性列表 */
    GENERIC_TA_CMD_GET_PROPERTY_COUNT = 4,      /* 获取属性数量 */

    /* 兼容现有命令 - 保持向后兼容 */
    GENERIC_TA_CMD_GET_PROPERTY_STRING = 10,
    GENERIC_TA_CMD_GET_PROPERTY_BOOL = 11,
    GENERIC_TA_CMD_GET_PROPERTY_U32 = 12,
    GENERIC_TA_CMD_GET_PROPERTY_U64 = 13,
};
```

## 7. 内核服务层接口

### 7.1 属性服务处理接口

| 项目 | 内容 |
|------|------|
| 函数接口 | `static int handle_property_request(struct property_request* req, struct property_response* resp)` |
| 输入 | req: 指向属性请求消息的指针，包含属性集、属性名称或索引<br>resp: 指向属性响应消息的指针，用于填充返回数据 |
| 输出 | resp: 填充属性查找结果，包括属性类型、名称、值和长度 |
| 返回值 | TEE_SUCCESS: 成功处理请求<br>TEE_ERROR_ITEM_NOT_FOUND: 属性未找到<br>TEE_ERROR_BAD_PARAMETERS: 请求参数无效 |
| 说明 | TIPC服务端处理属性查找请求，在内核属性数据库中查找指定属性 |

| 项目 | 内容 |
|------|------|
| 函数接口 | `static int handle_property_enum_request(struct property_enum_request* req, struct property_enum_response* resp)` |
| 输入 | req: 指向属性枚举请求的指针，包含属性集、起始索引和请求数量<br>resp: 指向属性枚举响应的指针，用于填充返回数据 |
| 输出 | resp: 填充枚举结果，包括总数量、返回数量和属性列表 |
| 返回值 | TEE_SUCCESS: 成功处理枚举请求<br>TEE_ERROR_BAD_PARAMETERS: 请求参数无效 |
| 说明 | TIPC服务端处理属性枚举请求，返回指定范围内的属性信息 |

### 7.2 内核属性管理接口

| 项目 | 内容 |
|------|------|
| 函数接口 | `static TEE_Result get_client_properties(uint32_t client_id, const char* prop_name, struct property_response* resp)` |
| 输入 | client_id: 客户端标识符<br>prop_name: 属性名称字符串<br>resp: 指向属性响应消息的指针 |
| 输出 | resp: 填充客户端属性信息 |
| 返回值 | TEE_SUCCESS: 成功获取客户端属性<br>TEE_ERROR_ITEM_NOT_FOUND: 客户端属性未找到 |
| 说明 | 根据客户端ID动态生成客户端相关属性，如身份信息等 |

| 项目 | 内容 |
|------|------|
| 函数接口 | `static TEE_Result copy_property_to_response(const struct user_ta_property* prop, struct property_response* resp)` |
| 输入 | prop: 指向属性结构体的指针<br>resp: 指向属性响应消息的指针 |
| 输出 | resp: 填充属性信息到响应消息 |
| 返回值 | TEE_SUCCESS: 成功复制属性<br>TEE_ERROR_SHORT_BUFFER: 缓冲区不足<br>TEE_ERROR_BAD_FORMAT: 不支持的属性类型 |
| 说明 | 将内核属性结构体的数据复制到TIPC响应消息中，支持所有7种GP标准类型 |

| 项目 | 内容 |
|------|------|
| 函数接口 | `static int handle_property_by_index_request(struct property_request* req, struct property_response* resp)` |
| 输入 | req: 指向按索引属性请求的指针<br>resp: 指向属性响应消息的指针 |
| 输出 | resp: 填充属性查找结果 |
| 返回值 | TEE_SUCCESS: 成功处理请求<br>TEE_ERROR_ITEM_NOT_FOUND: 索引超出范围<br>TEE_ERROR_BAD_PARAMETERS: 请求参数无效 |
| 说明 | TIPC服务端处理按索引获取属性请求，支持枚举器的远程属性访问 |

| 项目 | 内容 |
|------|------|
| 函数接口 | `static int handle_property_count_request(struct property_request* req, struct property_response* resp)` |
| 输入 | req: 指向属性数量请求的指针<br>resp: 指向属性响应消息的指针 |
| 输出 | resp: 填充属性数量信息 |
| 返回值 | TEE_SUCCESS: 成功处理请求<br>TEE_ERROR_BAD_PARAMETERS: 请求参数无效 |
| 说明 | TIPC服务端处理获取属性数量请求，返回指定属性集的属性总数 |

| 项目 | 内容 |
|------|------|
| 函数接口 | `static int handle_generic_ta_command(uint32_t cmd, void* req_buf, size_t req_size, void* resp_buf, size_t resp_size)` |
| 输入 | cmd: TIPC命令类型<br>req_buf: 请求缓冲区<br>req_size: 请求大小<br>resp_buf: 响应缓冲区<br>resp_size: 响应大小 |
| 输出 | resp_buf: 填充响应数据 |
| 返回值 | TEE_SUCCESS: 成功处理命令<br>TEE_ERROR_NOT_SUPPORTED: 不支持的命令<br>TEE_ERROR_BAD_PARAMETERS: 参数无效 |
| 说明 | 统一的TIPC命令分发器，处理所有GP标准属性相关命令 |

### 7.3 属性数据库接口

| 项目 | 内容 |
|------|------|
| 函数接口 | `static TEE_Result enumerate_client_properties(struct property_enum_request* req, struct property_enum_response* resp)` |
| 输入 | req: 指向枚举请求的指针<br>resp: 指向枚举响应的指针 |
| 输出 | resp: 填充客户端属性枚举结果 |
| 返回值 | TEE_SUCCESS: 成功枚举客户端属性<br>TEE_ERROR_BAD_PARAMETERS: 请求参数无效 |
| 说明 | 枚举客户端相关的动态属性，生成属性列表 |

## 8. 接口使用示例

### 8.1 GP标准属性获取示例

```c
/* 获取字符串属性示例 */
TEE_Result get_tee_version_example(void) {
    char version[64];
    size_t version_len = sizeof(version);
    TEE_Result res;

    res = TEE_GetPropertyAsString(TEE_PROPSET_TEE_IMPLEMENTATION,
                                  "gpd.tee.internalCore.version",
                                  version, &version_len);
    if (res == TEE_SUCCESS) {
        /* 使用version字符串 */
        printf("TEE version: %s\n", version);
    }

    return res;
}

/* 获取UUID属性示例 */
TEE_Result get_client_uuid_example(void) {
    TEE_UUID client_uuid;
    TEE_Result res;

    res = TEE_GetPropertyAsUUID(TEE_PROPSET_CURRENT_CLIENT,
                               "gpd.client.uuid",
                               &client_uuid);
    if (res == TEE_SUCCESS) {
        /* 使用client_uuid */
        printf("Client UUID: %08x-%04x-%04x\n",
               client_uuid.timeLow, client_uuid.timeMid,
               client_uuid.timeHiAndVersion);
    }

    return res;
}

/* 获取身份属性示例 */
TEE_Result get_client_identity_example(void) {
    TEE_Identity client_identity;
    TEE_Result res;

    res = TEE_GetPropertyAsIdentity(TEE_PROPSET_CURRENT_CLIENT,
                                   "gpd.client.identity",
                                   &client_identity);
    if (res == TEE_SUCCESS) {
        /* 使用client_identity */
        printf("Client login type: %u\n", client_identity.login);
    }

    return res;
}
```

### 8.2 OP-TEE风格属性枚举示例

```c
/* 枚举TEE属性示例 - OP-TEE风格 */
TEE_Result enumerate_tee_properties_example(void) {
    TEE_PropSetHandle enumerator;
    char prop_name[64];
    size_t name_len;
    TEE_Result res;

    /* 分配枚举器 */
    res = TEE_AllocatePropertyEnumerator(&enumerator);
    if (res != TEE_SUCCESS)
        return res;

    /* 启动枚举 */
    TEE_StartPropertyEnumerator(enumerator, TEE_PROPSET_TEE_IMPLEMENTATION);

    /* 遍历属性 */
    while (true) {
        name_len = sizeof(prop_name);
        res = TEE_GetPropertyName(enumerator, prop_name, &name_len);
        if (res == TEE_ERROR_ITEM_NOT_FOUND)
            break;
        if (res != TEE_SUCCESS)
            goto cleanup;

        printf("Found property: %s\n", prop_name);

        res = TEE_GetNextProperty(enumerator);
        if (res == TEE_ERROR_ITEM_NOT_FOUND)
            break;
        if (res != TEE_SUCCESS)
            goto cleanup;
    }

cleanup:
    TEE_FreePropertyEnumerator(enumerator);
    return TEE_SUCCESS;
}
```

### 8.3 内部接口使用示例

```c
/* 内部属性获取示例 */
static TEE_Result internal_property_access_example(void) {
    const struct user_ta_property* eps;
    size_t eps_len;
    enum user_ta_prop_type type = USER_TA_PROP_TYPE_STRING;
    char buffer[256];
    uint32_t buffer_len = sizeof(buffer);
    TEE_Result res;

    /* 获取本地属性集 */
    res = propset_get(TEE_PROPSET_TEE_IMPLEMENTATION, &eps, &eps_len);
    if (res != TEE_SUCCESS)
        return res;

    /* 查找特定属性 */
    for (size_t i = 0; i < eps_len; i++) {
        if (!strcmp(eps[i].name, "gpd.tee.systemTime.protectionLevel")) {
            /* 提取属性值 */
            res = propget_get_ext_prop(&eps[i], &type, buffer, &buffer_len);
            if (res == TEE_SUCCESS) {
                printf("Protection level: %s\n", buffer);
            }
            break;
        }
    }

    return res;
}
```

## 9. 总结

本文档提供了Trusty TEE GP标准属性API的完整内部接口规范，包括：

- **12个GP标准API**：完整的属性获取和枚举功能
- **23个内部接口**：核心管理、TIPC服务、辅助函数、内核服务
- **8个数据结构**：OP-TEE风格、TIPC协议、GP标准结构
- **统一设计理念**：基于OP-TEE的成熟架构，确保设计一致性和可靠性

所有接口都遵循GP标准规范，采用OP-TEE的成熟设计模式，为Trusty TEE提供了完整、高效、可靠的属性管理能力。
