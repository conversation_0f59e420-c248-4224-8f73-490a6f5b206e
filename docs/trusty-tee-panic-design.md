# Trusty TEE GP Panic 功能设计方案

## 1. 概述

### 1.1 设计目标
设计并实现 GP TEE 标准的 `TEE_Panic(TEE_Result panicCode)` 功能，利用现有 Trusty 系统调用 `_rctee_exit_etc` 和 `rctee_app_exit_etc` 函数实现错误消息传递和程序终止。

### 1.2 设计原则
- **最小修改原则**：最大化复用现有 Trusty 基础设施
- **GP 标准兼容**：严格遵循 GP Internal Core API 规范
- **错误传递机制**：确保 panic 错误码能够传递给对端
- **系统稳定性**：保证 panic 后系统能够正确清理和终止

## 2. 整体架构设计

### 2.1 调用流程

```
TEE_Panic(panicCode)
    ↓
_rctee_exit_etc(EXIT_FAILURE, panicCode | PANIC_FLAG)
    ↓
sys_exit_etc(EXIT_FAILURE, panicCode | PANIC_FLAG)
    ↓
rctee_app_exit(EXIT_FAILURE) → rctee_app_exit_etc(EXIT_FAILURE, panicCode | PANIC_FLAG, true)
    ↓
错误消息通知对端 + 应用清理 + panic("\n")
```

### 2.2 两层架构（利用现有系统调用）

1. **GP API 层**：`TEE_Panic` 函数实现
2. **内核处理层**：扩展现有 `sys_exit_etc` 和 `rctee_app_exit_etc` 支持 panic 场景

## 3. 详细设计方案

### 3.1 GP API 层设计

#### 3.1.1 函数接口
```c
void TEE_Panic(TEE_Result panicCode);
```

#### 3.1.2 实现策略
- **位置**：`user/base/lib/libutee/tee_panic.c`
- **功能**：参数验证 + 现有系统调用封装
- **特点**：`__NO_RETURN` 属性，确保函数不返回

#### 3.1.3 实现要点
1. 验证 `panicCode` 参数有效性
2. 设置 panic 标识位：`panicCode | PANIC_FLAG_MASK`
3. 调用现有系统调用：`_rctee_exit_etc(EXIT_FAILURE, panicCode | PANIC_FLAG_MASK)`
4. 添加备用终止机制（防止系统调用失败）

#### 3.1.4 Panic 标识机制
- **PANIC_FLAG_MASK**：`0x80000000` (最高位标识为 panic)
- **错误码保留**：低31位保留原始 `TEE_Result` 值
- **识别方式**：内核通过检查最高位识别 panic 调用

### 3.2 内核处理层设计

#### 3.2.1 sys_exit_etc 函数增强
- **现有接口保持不变**：`long sys_exit_etc(int32_t status, uint32_t flags)`
- **panic 识别**：通过 `flags` 参数的最高位识别 panic 调用
- **处理逻辑**：
  ```c
  if (flags & PANIC_FLAG_MASK) {
      // panic 处理路径
      uint32_t panic_code = flags & ~PANIC_FLAG_MASK;
      rctee_app_crash(panic_code);  // 调用 crash 路径
  } else {
      // 正常退出路径
      rctee_app_exit(status);
  }
  ```

#### 3.2.2 rctee_app_exit_etc 增强
- **现有功能保持不变**：应用清理、通知机制、状态管理
- **panic 特殊处理**：在函数末尾添加 panic 检测和调用
- **错误码传递**：确保 `panic_code` 能够通过 crash notifier 传递给对端

#### 3.2.3 Panic 终止机制
- **检测位置**：`rctee_app_exit_etc` 函数末尾，在 `rctee_thread_exit` 之前
- **检测条件**：`report_crash == true` 且 `crash_reason` 包含 panic 标识
- **终止调用**：`panic("TEE_Panic: 0x%08x\n", crash_reason & ~PANIC_FLAG_MASK)`

## 4. 错误消息传递机制

### 4.1 现有通知机制利用
- **crash notifier**：利用现有的 `notifier->crash(app, crash_reason)` 机制
- **错误码传递**：`panic_code` (去除标识位) 作为 `crash_reason` 参数传递
- **对端通知**：通过 TIPC 或其他 IPC 机制通知对端应用

### 4.2 错误码格式设计
- **输入格式**：GP 标准 `TEE_Result` (32位)
- **传输格式**：`TEE_Result | 0x80000000` (添加 panic 标识位)
- **存储格式**：内核中作为 `uint32_t crash_reason` 传递
- **输出格式**：对端接收时去除标识位，恢复原始 `TEE_Result`

### 4.3 标识位机制
```c
#define PANIC_FLAG_MASK     0x80000000
#define IS_PANIC_CODE(code) ((code) & PANIC_FLAG_MASK)
#define GET_PANIC_CODE(code) ((code) & ~PANIC_FLAG_MASK)
#define SET_PANIC_CODE(code) ((code) | PANIC_FLAG_MASK)
```

### 4.4 对端处理
- **错误接收**：对端通过现有错误通知机制接收
- **错误识别**：检查最高位判断是否为 TEE_Panic 错误
- **错误处理**：提取原始 `TEE_Result`，按 GP 规范处理 panic 事件

## 5. 系统终止机制

### 5.1 终止流程
1. **应用清理**：释放资源、关闭连接、清理状态
2. **通知发送**：向对端发送 crash 通知（包含 panic 标识）
3. **最终终止**：调用 `panic("\n")` 确保系统级终止

### 5.2 panic 调用位置和条件
- **检测位置**：`rctee_app_exit_etc` 函数末尾，在 `rctee_thread_exit(status)` 之前
- **检测条件**：`report_crash == true && IS_PANIC_CODE(crash_reason)`
- **调用格式**：`panic("TEE_Panic: 0x%08x\n", GET_PANIC_CODE(crash_reason))`

### 5.3 修改点设计
```c
// 在 rctee_app_exit_etc 函数末尾添加：
if (report_crash && IS_PANIC_CODE(crash_reason)) {
    panic("TEE_Panic: 0x%08x\n", GET_PANIC_CODE(crash_reason));
}
rctee_thread_exit(status);
```

### 5.4 具体的错误消息传递设计

#### 5.4.1 crash notifier 机制详解
现有的 `rctee_app_exit_etc` 函数中已有完整的 crash notifier 调用：
```c
if (report_crash) {
    list_for_every_entry(&app_notifier_list, notifier,
                         struct rctee_app_notifier, node) {
        if (!notifier->crash) {
            continue;
        }
        ret = notifier->crash(app, crash_reason);  // crash_reason 包含 panic 错误码
        if (ret != NO_ERROR) {
            panic("crash notifier failed(%d) for app %u, %s\n", ret,
                  app->app_id, app->props.app_name);
        }
    }
}
```

#### 5.4.2 对端消息传递机制
- **notifier->crash 函数**：负责将 `crash_reason`（包含 panic 错误码）发送给对端
- **TIPC 通道**：通过现有的 TIPC 连接向对端发送 crash 事件
- **错误码格式**：对端接收到的 `crash_reason` 包含 panic 标识位和原始 TEE_Result

#### 5.4.3 资源清理机制详解
现有的 `rctee_app_exit_etc` 函数已包含完整的清理流程：
```c
// 1. shutdown notifier 调用 - 清理应用特定资源
list_for_every_entry(&app_notifier_list, notifier, struct rctee_app_notifier, node) {
    if (!notifier->shutdown) continue;
    ret = notifier->shutdown(app);
}

// 2. 释放应用内存
free(app->als);
app->als = NULL;

// 3. 状态转换
mutex_acquire(&apps_lock);
app->state = APP_TERMINATING;
mutex_release(&apps_lock);

// 4. 通知应用管理器
event_signal(&app_mgr_event, false);
```

## 6. 具体修改点设计

### 6.1 TEE_Panic 函数实现
```c
void TEE_Panic(TEE_Result panicCode) {
    // 参数验证：确保最高位为0，避免与标识位冲突
    if (panicCode & 0x80000000) {
        panicCode &= 0x7FFFFFFF;  // 清除最高位
    }

    // 设置 panic 标识位并调用现有系统调用
    uint32_t panic_flags = panicCode | 0x80000000;
    _rctee_exit_etc(EXIT_FAILURE, panic_flags);

    // 备用终止机制（理论上不会执行到这里）
    abort();
}
```

### 6.2 sys_exit_etc 函数修改
```c
long sys_exit_etc(int32_t status, uint32_t flags) {
    thread_t* current = get_current_thread();
    LTRACEF("exit called, thread %p, name %s\n", current, current->name);

    // 检查是否为 panic 调用
    if (flags & 0x80000000) {
        // panic 路径：提取原始错误码并调用 crash 处理
        uint32_t panic_code = flags & 0x7FFFFFFF;
        rctee_app_crash(panic_code);
    } else {
        // 正常退出路径
        rctee_app_exit(status);
    }
    return 0L;
}
```

### 6.3 rctee_app_exit_etc 函数修改
在现有函数的末尾，`rctee_thread_exit(status)` 调用之前添加：
```c
static void __NO_RETURN rctee_app_exit_etc(int status,
                                            uint32_t crash_reason,
                                            bool report_crash) {
    // ... 现有的所有清理和通知逻辑保持不变 ...

    // 在函数末尾添加 panic 检测和处理
    if (report_crash && (crash_reason & 0x80000000)) {
        // 提取原始 TEE_Result 错误码
        uint32_t tee_result = crash_reason & 0x7FFFFFFF;
        // 打印 panic 错误信息并终止系统
        panic("TEE_Panic: 0x%08x\n", tee_result);
    }

    rctee_thread_exit(status);
}
```

### 6.4 错误信息打印设计
- **打印位置**：在 `rctee_app_exit_etc` 函数末尾
- **打印条件**：`report_crash == true` 且 `crash_reason` 包含 panic 标识位
- **打印格式**：`panic("TEE_Panic: 0x%08x\n", tee_result)`
- **系统行为**：panic 函数会打印错误信息并停止系统运行

### 6.5 完整的执行流程
1. **TEE_Panic 调用**：用户空间调用 `TEE_Panic(TEE_ERROR_GENERIC)`
2. **系统调用**：`_rctee_exit_etc(EXIT_FAILURE, 0x80000000 | TEE_ERROR_GENERIC)`
3. **内核识别**：`sys_exit_etc` 检测到 panic 标识位
4. **crash 处理**：调用 `rctee_app_crash(TEE_ERROR_GENERIC)`
5. **资源清理**：执行 shutdown notifier，释放内存，状态转换
6. **消息传递**：crash notifier 将错误码发送给对端
7. **错误打印**：`panic("TEE_Panic: 0xFFFF0000\n")` 并停止系统

## 7. 具体修改点

### 7.1 用户空间修改
- **新增文件**：`user/base/lib/libutee/tee_panic.c`
- **头文件声明**：在相应头文件中添加 `TEE_Panic` 声明
- **编译配置**：更新 Makefile 包含新文件

### 7.2 内核空间修改
- **sys_exit_etc 函数**：添加 panic 标识检测和分发逻辑
- **rctee_app_exit_etc 函数**：在末尾添加 panic 检测和调用
- **常量定义**：添加 PANIC_FLAG_MASK 等常量定义

### 7.3 兼容性保证
- **现有接口不变**：所有现有函数接口保持不变
- **现有行为不变**：非 panic 调用的行为完全不变
- **标识位安全**：使用最高位作为标识，不影响正常错误码

## 8. 测试策略

### 8.1 单元测试
- **TEE_Panic 函数测试**：验证各种错误码的处理
- **标识位测试**：验证 panic 标识位的正确设置和识别
- **错误传递测试**：验证错误码的正确传递和恢复

### 8.2 集成测试
- **端到端测试**：验证完整的 panic 流程
- **对端通知测试**：验证对端能够正确接收和识别 panic 错误
- **系统稳定性测试**：验证 panic 后系统的稳定性

### 8.3 兼容性测试
- **现有应用测试**：确保现有应用的正常退出不受影响
- **并发测试**：验证 panic 与正常退出的并发处理
- **边界测试**：测试各种边界条件和异常情况

## 9. 总结

### 9.1 方案优势
1. **零新增系统调用**：完全复用现有 `_rctee_exit_etc` 系统调用
2. **最小修改**：只需修改两个内核函数，影响范围极小
3. **标准兼容性**：严格遵循 GP TEE 标准规范
4. **系统稳定性**：确保 panic 后系统的正确清理和终止
5. **向后兼容**：现有功能完全不受影响

### 9.2 关键特性
- **GP 标准 TEE_Panic 接口**：完整实现 GP 规范要求
- **现有系统调用复用**：利用 `_rctee_exit_etc` 进入内核
- **错误消息传递**：通过现有 crash notifier 机制传递错误
- **可靠终止机制**：确保应用和系统的正确终止
- **标识位机制**：巧妙利用最高位区分 panic 和正常退出

### 9.3 实现路径
1. **第一阶段**：实现 TEE_Panic 函数，利用现有系统调用
2. **第二阶段**：修改 sys_exit_etc 添加 panic 识别逻辑
3. **第三阶段**：修改 rctee_app_exit_etc 添加 panic 终止调用
4. **第四阶段**：完善测试验证和文档

### 9.4 核心设计亮点
- **利用现有基础设施**：无需新增系统调用，最大化复用
- **巧妙的标识机制**：通过最高位标识 panic，简单有效
- **最小化修改原则**：只在关键路径添加必要的 panic 处理
- **完整的错误传递**：确保 panic 错误码能够可靠传递给对端
