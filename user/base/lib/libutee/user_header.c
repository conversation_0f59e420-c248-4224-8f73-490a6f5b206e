#include <stddef.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdbool.h>
#include <rctee_user_ipc.h>
#include <uapi/err.h>
#define TLOG_TAG "libutee"
#include <trusty_log.h>
#include "libutee.h"
#include <trusty/uuid.h>
#include <user_ta_header.h>

/* Request/Response bits */
#define RK_REQ_SHIFT 1
#define RK_RESP_BIT (1 << RK_REQ_SHIFT)

#define MAX_BUFFER_LENGTH 1024 

/* Forward declarations */
static void handle_port(uevent_t* ev);
static void handle_channel(uevent_t* ev);
static long handle_msg(handle_t chan);
static long ripc_err_to_rctee_err(long ripc_err);

int main(void) {
    long rc;
    uevent_t event;
    handle_t port;
    char uuid_str[UUID_STR_SIZE];

    TLOGI("Starting rctee TA service\n");
    /* 将 UUID 转换为字符串作为 port_name */
    uuid_to_str(&uuid, uuid_str);
    /* Initialize service */
    rc = port_create(uuid_str, 1, MAX_BUFFER_LENGTH, used_perms);
    RCTEE_OnInit();
    if (rc < 0) {
        TLOGE("[MAIN] Failed to create port: %ld\n", rc);
        return ripc_err_to_rctee_err(rc);
    }

    port = (handle_t)rc;
    /* 进入主事件循环 */
    while (true) {
        event.handle = INVALID_IPC_HANDLE;
        event.event = 0;
        event.cookie = NULL;

        rc = wait_any(&event, INFINITE_TIME);
        if (rc < 0) {
            TLOGE("[MAIN] Event loop exiting: %ld\n", rc);
            break;
        }

        if (rc == NO_ERROR) {
            if (event.handle == port) {
                handle_port(&event);
            } else {
                handle_channel(&event);
            }
        }
    }

    // 清理资源
    if (port != INVALID_IPC_HANDLE) {
        rc = close(port);
        if (rc < 0) {
            TLOGE("[MAIN] Failed to close port: %ld\n", rc);
        }
    }
    return rc;
}

static void handle_port(uevent_t* ev) {
    // 检查错误事件
    if ((ev->event & IPC_HANDLE_POLL_ERROR) ||
        (ev->event & IPC_HANDLE_POLL_HUP) ||
        (ev->event & IPC_HANDLE_POLL_MSG) ||
        (ev->event & IPC_HANDLE_POLL_SEND_UNBLOCKED)) {
        TLOGE("[PORT] Error event: 0x%x\n", ev->event);
        return;
    }
    uuid_t peer_uuid;
    // 处理新连接
    if (ev->event & IPC_HANDLE_POLL_READY) {
        int rc = accept(ev->handle, &peer_uuid);
        if (rc < 0) {
            TLOGE("[PORT] Accept failed: %d\n", rc);
            return;
        }
    }
}

static void handle_channel(uevent_t* ev) {
    if ((ev->event & IPC_HANDLE_POLL_ERROR) ||
        (ev->event & IPC_HANDLE_POLL_READY)) {
        TLOGE("[CHAN] Error event: 0x%x\n", ev->event);
        abort();
    }

    handle_t chan = ev->handle;

    if (ev->event & IPC_HANDLE_POLL_MSG) {
        long rc = handle_msg(chan);
        if (rc != ERROR_NONE) {
            TLOGE("[CHAN] Message handling failed: %ld\n", rc);
            close(chan);
        }
    }

    if (ev->event & IPC_HANDLE_POLL_HUP) {
        close(chan);
        return;
    }
}

static long ripc_err_to_rctee_err(long ripc_err) {
    switch (ripc_err) {
    case NO_ERROR:
        return ERROR_NONE;
    case ERR_BAD_LEN:
    case ERR_NOT_VALID:
    case ERR_NOT_IMPLEMENTED:
    case ERR_NOT_SUPPORTED:
        return ERROR_INVALID;
    default:
        return ERROR_UNKNOWN;
    }
}

static long send_response(handle_t chan,
    uint32_t cmd,
    uint8_t* out_buf,
    uint32_t out_buf_size) {
    struct RCTEE_Message rk_msg = {
    .cmd = cmd | RK_RESP_BIT,
    .out_len = out_buf_size,
    };
    struct iovec iov[2] = {
            {&rk_msg, sizeof(rk_msg)},
            {out_buf, out_buf_size},
    };
    ipc_msg_t msg = {
        .num_iov = 2,
        .iov = iov,
        .num_handles = 0,
        .handles = NULL
    };

    /* send message back to the caller */
    long rc = send_msg(chan, &msg);

    // fatal error
    if (rc < 0) {
        TLOGE("[RESP] Send ultimately failed: %ld\n", rc);
        return ripc_err_to_rctee_err(rc);
    }

    return ERROR_NONE;
}

static long send_error_response(handle_t chan, uint32_t cmd, long err) {
    return send_response(chan, cmd, (uint8_t*)&err, sizeof(err));
}

static long handle_msg(handle_t chan) {
    ipc_msg_info_t msg_inf;
    uint8_t* msg_buf = NULL;
    uint8_t* out_buf = NULL;
    size_t out_buf_size = 0;
    long err = ERROR_NONE;

    long rc = get_msg(chan, &msg_inf);
    if (rc == ERR_NO_MSG)
        return ERROR_NONE;

    if (rc != NO_ERROR) {
        TLOGE("[MSG] Failed to get message: %ld\n", rc);
        return ripc_err_to_rctee_err(rc);
    }

    /* 分配消息缓冲区 */
    if ((msg_buf = malloc(msg_inf.len)) == NULL) {
        put_msg(chan, msg_inf.id);
        return ERROR_OUT_OF_MEMORY;
    }

    struct iovec iov = {
        .iov_base = msg_buf,
        .iov_len = msg_inf.len
    };

    ipc_msg_t msg = {
        .num_iov = 1,
        .iov = &iov,
        .num_handles = 0,
        .handles = NULL
    };

    rc = read_msg(chan, msg_inf.id, 0, &msg);
    put_msg(chan, msg_inf.id);

    if (rc < 0) {
        TLOGE("[MSG] Failed to read message: %ld\n", rc);
        free(msg_buf);
        return ripc_err_to_rctee_err(rc);
    }

    if (((size_t)rc) < sizeof(struct RCTEE_Message)) {
        TLOGE("[MSG] Invalid message size: %zu\n", (size_t)rc);
        free(msg_buf);
        return ERROR_INVALID;
    }

    const size_t header_size = sizeof(struct RCTEE_Message);
    struct RCTEE_Message* rctee_msg = (struct RCTEE_Message*)msg_buf;
    uint8_t* payload = msg_buf + header_size;

    if (rctee_msg->msg_type == RCTEEC_MESSAGE_TYPE_CALL) {
        err = RCTEE_OnCall(rctee_msg->cmd, payload,
            rctee_msg->in_len,
            &out_buf,
            &out_buf_size);

        if (err != ERROR_NONE) {
            TLOGE("[CALL] Request processing failed: %ld\n", err);
            send_error_response(chan, rctee_msg->cmd, err);
            goto cleanup;
        }

        err = send_response(chan, rctee_msg->cmd, out_buf, out_buf_size);
    } else if (rctee_msg->msg_type == RCTEEC_MESSAGE_TYPE_CONNECT) {
        // 处理连接类型消息
        err = RCTEE_OnConnect();
        if (err == ERROR_NONE) {
            err = send_response(chan, rctee_msg->cmd, (uint8_t*)&err, sizeof(err));
        }
    } else if (rctee_msg->msg_type == RCTEEC_MESSAGE_TYPE_DISCONNECT) {
        // 处理断开连接类型消息
        TLOGI("Received DISCONNECT message, client disconnecting\n");
        RCTEE_OnDisConnect(NULL);
        err = ERROR_NONE;
        err = send_response(chan, rctee_msg->cmd, (uint8_t*)&err, sizeof(err));
    } else {
        // 未知的消息类型
        TLOGE("[MSG] Unknown message type: %d\n", rctee_msg->msg_type);
        err = ERROR_INVALID;
    }

cleanup:
    free(msg_buf);
    free(out_buf);
    return err;
}






