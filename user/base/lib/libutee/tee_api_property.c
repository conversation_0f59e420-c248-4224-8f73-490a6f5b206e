#include <stddef.h>
#include <stdint.h>
#include <stdbool.h>
#include <string.h>
#include <stdio.h>
#include <stdlib.h>
#include <user_ta_header.h>
#include <tee_api_defines.h>
#include <tee_api_types.h>
#include <lib/tipc/tipc.h>
#include <lib/tipc/tipc_srv.h>
#define TLOG_TAG "libutee"
#define TLOG_LVL TLOG_LVL_DEBUG
#include <trusty_log.h>
#include <uapi/err.h>



/* Generic TA service port name */
#define GENERIC_TA_PORT "com.generic.ta.service"

/* Convert TEE error code to system error code */
static int32_t tee_to_sys_error(TEE_Result res) {
    switch (res) {
    case TEE_SUCCESS:
        return NO_ERROR;
    case TEE_ERROR_ITEM_NOT_FOUND:
        return ERR_NOT_FOUND;
    case TEE_ERROR_BAD_FORMAT:
        return ERR_NOT_VALID;
    case TEE_ERROR_OUT_OF_MEMORY:
        return ERR_NO_MEMORY;
    case TEE_ERROR_BUSY:
        return ERR_BUSY;
    case TEE_ERROR_COMMUNICATION:
        return ERR_IO;
    case TEE_ERROR_SHORT_BUFFER:
        return ERR_NOT_ENOUGH_BUFFER;
    case TEE_ERROR_GENERIC:
    default:
        return ERR_GENERIC;
    }
}

/* Convert system error code to TEE error code */
static TEE_Result sys_to_tee_error(int32_t res) {
    switch (res) {
    case NO_ERROR:
        return TEE_SUCCESS;
    case ERR_NOT_FOUND:
        return TEE_ERROR_ITEM_NOT_FOUND;
    case ERR_NOT_VALID:
        return TEE_ERROR_BAD_FORMAT;
    case ERR_NO_MEMORY:
        return TEE_ERROR_OUT_OF_MEMORY;
    case ERR_BUSY:
        return TEE_ERROR_BUSY;
    case ERR_IO:
        return TEE_ERROR_COMMUNICATION;
    case ERR_NOT_ENOUGH_BUFFER:
        return TEE_ERROR_SHORT_BUFFER;
    default:
        return TEE_ERROR_GENERIC;
    }
}

const struct user_ta_property tee_props[] = {};

static TEE_Result propset_get(TEE_PropSetHandle h,
    const struct user_ta_property** eps,
    size_t* eps_len)
{
    if (h == TEE_PROPSET_CURRENT_TA) {
        *eps = ta_props;
        *eps_len = ta_num_props;
    } else if (h == TEE_PROPSET_CURRENT_CLIENT) {
        *eps = NULL;
        *eps_len = 0;
    } else if (h == TEE_PROPSET_TEE_IMPLEMENTATION) {
        *eps = tee_props;
        *eps_len = sizeof(tee_props) / sizeof(tee_props[0]);
    } else {
        return TEE_ERROR_ITEM_NOT_FOUND;
    }

    return TEE_SUCCESS;
}

static TEE_Result propget_get_ext_prop(const struct user_ta_property* ep,
    enum user_ta_prop_type* type,
    void* buf, uint32_t* len)
{
    size_t l;

    *type = ep->type;
    switch (*type) {
    case USER_TA_PROP_TYPE_BOOL:
        l = sizeof(bool);
        break;
    case USER_TA_PROP_TYPE_U32:
        l = sizeof(uint32_t);
        break;
    case USER_TA_PROP_TYPE_U64:
        l = sizeof(uint64_t);
        break;
    case USER_TA_PROP_TYPE_STRING:
        /* take the leading 0 into account */
        l = strlen(ep->value) + 1;
        break;
    default:
        return TEE_ERROR_GENERIC;
    }

    if (*len < l) {
        *len = l;
        return TEE_ERROR_SHORT_BUFFER;
    }

    *len = l;
    memcpy(buf, ep->value, l);
    return TEE_SUCCESS;
}

static bool is_propset_pseudo_handle(TEE_PropSetHandle h)
{
    return h == TEE_PROPSET_CURRENT_TA ||
        h == TEE_PROPSET_CURRENT_CLIENT ||
        h == TEE_PROPSET_TEE_IMPLEMENTATION;
}

/* Helper function to read response from service */
static int property_read(handle_t chan,
    size_t min_sz,
    void* buf,
    size_t buf_sz) {
    int rc;
    ipc_msg_info_t msg_inf;

    /* 等待消息就绪 */
    uevent_t event;
    rc = wait(chan, &event, 5000); /* 设置5秒超时 */
    if (rc < 0) {
        TLOGE("Failed to wait for response (%d)\n", rc);
        return rc;
    }

    if (!(event.event & IPC_HANDLE_POLL_MSG)) {
        TLOGE("Failed to receive response event\n");
        return ERR_NOT_READY;
    }

    /* 获取消息信息 */
    rc = get_msg(chan, &msg_inf);
    if (rc < 0) {
        TLOGE("Failed to get message info (%d)\n", rc);
        return rc;
    }

    /* 检查消息大小 */
    if (msg_inf.len < min_sz) {
        TLOGE("Message too short (%zu < %zu)\n", msg_inf.len, min_sz);
        rc = TEE_ERROR_SHORT_BUFFER;
        goto err_bad_msg;
    }

    if (msg_inf.len > buf_sz) {
        TLOGE("Message too long (%zu > %zu)\n", msg_inf.len, buf_sz);
        rc = TEE_ERROR_SHORT_BUFFER;
        goto err_bad_msg;
    }

    /* 创建ipc_msg_t结构来接收消息 */
    struct iovec iov = {
        .iov_base = buf,
        .iov_len = buf_sz,
    };

    ipc_msg_t msg = {
        .num_iov = 1,
        .iov = &iov,
        .num_handles = 0,
        .handles = NULL,
    };

    /* 读取消息内容 */
    rc = read_msg(chan, msg_inf.id, 0, &msg);
    if (rc < 0) {
        TLOGE("Failed to read message (%d)\n", rc);
        goto err_read_msg;
    }

    /* 检查读取的大小 */
    if (rc != (int)msg_inf.len) {
        TLOGE("Partial message read (%d != %zu)\n", rc, msg_inf.len);
        rc = ERR_IO;
        goto err_bad_read;
    }

    put_msg(chan, msg_inf.id);
    return msg_inf.len;

err_bad_read:
err_read_msg:
err_bad_msg:
    put_msg(chan, msg_inf.id);
    return rc;
}

static TEE_Result propget_get_property(TEE_PropSetHandle h, const char* name,
    enum user_ta_prop_type* type,
    void* buf, uint32_t* len)
{
    TEE_Result res = TEE_ERROR_ITEM_NOT_FOUND;
    const struct user_ta_property* eps;
    size_t eps_len;
    size_t n;

    /* First try to get from local properties */
    res = propset_get(h, &eps, &eps_len);
    if (res != TEE_SUCCESS)
        return res;

    for (n = 0; n < eps_len; n++) {
        if (!strcmp(name, eps[n].name))
            return propget_get_ext_prop(eps + n, type, buf, len);
    }

    /* If not found locally, try to get from service */
    handle_t chan;
    int rc = tipc_connect(&chan, GENERIC_TA_PORT);
    if (rc < 0) {
        TLOGE("Failed to connect to service (%d)\n", rc);
        return TEE_ERROR_COMMUNICATION;
    }

    struct property_request req;
    memset(&req, 0, sizeof(req));

    /* Set command based on property type */
    switch (*type) {
    case USER_TA_PROP_TYPE_STRING:
        req.hdr.cmd = GENERIC_TA_CMD_GET_PROPERTY_STRING;
        break;
    case USER_TA_PROP_TYPE_BOOL:
        req.hdr.cmd = GENERIC_TA_CMD_GET_PROPERTY_BOOL;
        break;
    case USER_TA_PROP_TYPE_U32:
        req.hdr.cmd = GENERIC_TA_CMD_GET_PROPERTY_U32;
        break;
    case USER_TA_PROP_TYPE_U64:
        req.hdr.cmd = GENERIC_TA_CMD_GET_PROPERTY_U64;
        break;
    default:
        TLOGE("Unsupported property type: %d\n", *type);
        close(chan);
        return TEE_ERROR_BAD_PARAMETERS;
    }

    strncpy(req.name, name, sizeof(req.name) - 1);
    req.name[sizeof(req.name) - 1] = '\0';

    /* Send request */
    rc = tipc_send1(chan, &req, sizeof(req));
    if (rc < 0) {
        TLOGE("Failed to send request (%d)\n", rc);
        close(chan);
        return TEE_ERROR_COMMUNICATION;
    }

    /* Receive response */
    struct property_response resp;
    rc = property_read(chan, sizeof(resp), &resp, sizeof(resp));
    if (rc < 0) {
        TLOGE("Failed to read response (%d)\n", rc);
        close(chan);
        return TEE_ERROR_COMMUNICATION;
    }

    if (resp.result != NO_ERROR) {
        TLOGE("Service returned error: %d\n", resp.result);
        close(chan);
        return sys_to_tee_error(resp.result);
    }

    /* Copy response to output buffer */
    TEE_Result copy_result = TEE_SUCCESS;
    switch (*type) {
    case USER_TA_PROP_TYPE_STRING:
    {
        size_t str_len = strlen(resp.str_value) + 1;
        if (*len >= str_len) {
            memcpy(buf, resp.str_value, str_len);
            *len = str_len;
        } else {
            memcpy(buf, resp.str_value, *len - 1);
            ((char*)buf)[*len - 1] = '\0';
            *len = str_len;
            copy_result = TEE_ERROR_SHORT_BUFFER;
        }
    }
    break;

    case USER_TA_PROP_TYPE_BOOL:
        if (*len >= sizeof(bool)) {
            *((bool*)buf) = resp.bool_value;
            *len = sizeof(bool);
        } else {
            copy_result = TEE_ERROR_SHORT_BUFFER;
        }
        break;

    case USER_TA_PROP_TYPE_U32:
        if (*len >= sizeof(uint32_t)) {
            *((uint32_t*)buf) = resp.u32_value;
            *len = sizeof(uint32_t);
        } else {
            copy_result = TEE_ERROR_SHORT_BUFFER;
        }
        break;

    case USER_TA_PROP_TYPE_U64:
        if (*len >= sizeof(uint64_t)) {
            *((uint64_t*)buf) = resp.u64_value;
            *len = sizeof(uint64_t);
        } else {
            copy_result = TEE_ERROR_SHORT_BUFFER;
        }
        break;

    default:
        copy_result = TEE_ERROR_BAD_FORMAT;
    }

    close(chan);
    return copy_result;
}

TEE_Result TEE_GetPropertyAsString(TEE_PropSetHandle propsetOrEnumerator,
    const char* name, char* value,
    size_t* value_len)
{
    TEE_Result res = TEE_ERROR_GENERIC;
    size_t l = 0;
    enum user_ta_prop_type type = USER_TA_PROP_TYPE_INVALID;
    void* tmp_buf = NULL;
    uint32_t tmp_len = *value_len;
    uint32_t uint32_val = 0;
    bool bool_val = false;

    if (!value_len)
        return TEE_ERROR_BAD_PARAMETERS;

    tmp_buf = malloc(tmp_len);
    if (!tmp_buf)
        return TEE_ERROR_OUT_OF_MEMORY;

    res = propget_get_property(propsetOrEnumerator, name, &type,
        tmp_buf, &tmp_len);
    if (res != TEE_SUCCESS) {
        if (res == TEE_ERROR_SHORT_BUFFER) {
            *value_len = tmp_len;
        }
        goto out;
    }

    switch (type) {
    case USER_TA_PROP_TYPE_BOOL:
        bool_val = *((bool*)tmp_buf);
        {
            const char* bool_str = bool_val ? "true" : "false";
            size_t src_len = strlen(bool_str) + 1;
            size_t copy_len = (*value_len >= src_len) ? src_len : *value_len;
            memcpy(value, bool_str, copy_len);
            l = src_len - 1;
        }
        break;

    case USER_TA_PROP_TYPE_U32:
        uint32_val = *((uint32_t*)tmp_buf);
        l = snprintf(value, *value_len, "%u", uint32_val);
        break;

    case USER_TA_PROP_TYPE_STRING:
    {
        size_t src_len = strlen((const char*)tmp_buf) + 1;
        size_t copy_len = (*value_len >= src_len) ? src_len : *value_len;
        memcpy(value, (const char*)tmp_buf, copy_len);
        l = src_len - 1;
    }
    break;

    default:
        res = TEE_ERROR_BAD_FORMAT;
        goto out;
    }

    l++;	/* include zero termination */

    if (l > *value_len)
        res = TEE_ERROR_SHORT_BUFFER;
    *value_len = l;
out:
    free(tmp_buf);
    return res;
}

TEE_Result TEE_GetPropertyAsBool(TEE_PropSetHandle propsetOrEnumerator,
    const char* name, bool* value)
{
    TEE_Result res;
    enum user_ta_prop_type type;
    uint32_t bool_len = sizeof(bool);


    type = USER_TA_PROP_TYPE_BOOL;
    res = propget_get_property(propsetOrEnumerator, name, &type,
        value, &bool_len);
    if (type != USER_TA_PROP_TYPE_BOOL)
        res = TEE_ERROR_BAD_FORMAT;
    if (res != TEE_SUCCESS)
        goto out;

out:
    return res;
}

TEE_Result TEE_GetPropertyAsU32(TEE_PropSetHandle propsetOrEnumerator,
    const char* name, uint32_t* value)
{
    TEE_Result res;
    enum user_ta_prop_type type;
    uint32_t uint32_len = sizeof(uint32_t);


    type = USER_TA_PROP_TYPE_U32;
    res = propget_get_property(propsetOrEnumerator, name, &type,
        value, &uint32_len);
    if (type != USER_TA_PROP_TYPE_U32)
        res = TEE_ERROR_BAD_FORMAT;

    return res;
}

TEE_Result TEE_GetPropertyAsU64(TEE_PropSetHandle propsetOrEnumerator,
    const char* name, uint64_t* value)
{
    TEE_Result res;
    enum user_ta_prop_type type;
    uint32_t uint64_len = sizeof(*value);


    type = USER_TA_PROP_TYPE_U64;
    res = propget_get_property(propsetOrEnumerator, name, &type,
        value, &uint64_len);
    if (type != USER_TA_PROP_TYPE_U64)
        res = TEE_ERROR_BAD_FORMAT;

    return res;
}
